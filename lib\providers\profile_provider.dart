import 'package:flutter/material.dart';

class UserProfile {
  final String? name;
  final String? email;
  final String? profileImage;
  final String? membershipLevel;
  final int? totalBookings;
  final int? completedStays;
  final int? rewardPoints;
  final int? upcomingBookings;
  final int? reviewsCount;
  
  UserProfile({
    this.name,
    this.email,
    this.profileImage,
    this.membershipLevel,
    this.totalBookings,
    this.completedStays,
    this.rewardPoints,
    this.upcomingBookings,
    this.reviewsCount,
  });
}

class ProfileProvider extends ChangeNotifier {
  UserProfile _userProfile = UserProfile();
  bool _isLoading = true;
  String? _error;
  
  UserProfile get userProfile => _userProfile;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Load user profile data
  Future<void> loadUserProfile() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();
      
      // Simulate API call with a delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock data - in real app, fetch from API or local storage
      _userProfile = UserProfile(
        name: '<PERSON>',
        email: '<EMAIL>',
        profileImage: null, // Set to null to show placeholder or provide a real URL
        membershipLevel: 'Gold',
        totalBookings: 12,
        completedStays: 9,
        rewardPoints: 2500,
        upcomingBookings: 2,
        reviewsCount: 7,
      );
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load profile: ${e.toString()}';
      notifyListeners();
    }
  }
  
  // Update user profile information
  Future<void> updateProfile({
    String? name,
    String? email,
    String? profileImage,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();
      
      // Simulate API call with a delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Update the profile
      _userProfile = UserProfile(
        name: name ?? _userProfile.name,
        email: email ?? _userProfile.email,
        profileImage: profileImage ?? _userProfile.profileImage,
        membershipLevel: _userProfile.membershipLevel,
        totalBookings: _userProfile.totalBookings,
        completedStays: _userProfile.completedStays,
        rewardPoints: _userProfile.rewardPoints,
        upcomingBookings: _userProfile.upcomingBookings,
        reviewsCount: _userProfile.reviewsCount,
      );
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to update profile: ${e.toString()}';
      notifyListeners();
    }
  }
  
  // For demonstration, add a method to artificially increase reward points
  void addRewardPoints(int points) {
    final currentPoints = _userProfile.rewardPoints ?? 0;
    
    _userProfile = UserProfile(
      name: _userProfile.name,
      email: _userProfile.email,
      profileImage: _userProfile.profileImage,
      membershipLevel: _userProfile.membershipLevel,
      totalBookings: _userProfile.totalBookings,
      completedStays: _userProfile.completedStays,
      rewardPoints: currentPoints + points,
      upcomingBookings: _userProfile.upcomingBookings,
      reviewsCount: _userProfile.reviewsCount,
    );
    
    notifyListeners();
  }
}