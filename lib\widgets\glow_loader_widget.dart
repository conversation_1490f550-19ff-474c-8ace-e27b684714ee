import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';

class GlowingAvatarLoader extends StatefulWidget {
  final String imageUrl;
  final double size;

  const GlowingAvatarLoader({
    Key? key,
    required this.imageUrl,
    this.size = 100.0,
  }) : super(key: key);

  @override
  _GlowingAvatarLoaderState createState() => _GlowingAvatarLoaderState();
}

class _GlowingAvatarLoaderState extends State<GlowingAvatarLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    _animation = Tween<double>(begin: 0.0, end: 15.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Container(
            width: widget.size + _animation.value,
            height: widget.size + _animation.value,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.3),
                  blurRadius: _animation.value * 1.5,
                  spreadRadius: _animation.value / 2,
                ),
                BoxShadow(
                  color: Colors.white.withOpacity(0.7),
                  blurRadius: _animation.value,
                  spreadRadius: _animation.value / 3,
                ),
              ],
            ),
            child: CircleAvatar(
              radius: widget.size / 2,
              backgroundColor: AppColors.background,
              child: Center(
                child: ClipOval(
                  child: Image.asset(
                    widget.imageUrl,
                    width: widget.size * 0.8, // Slightly smaller to ensure it fits perfectly
                    height: widget.size * 0.8,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
