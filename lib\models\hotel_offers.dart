// To parse this JSON data, do
//
//     final hoteloffers = hoteloffersFromJson(jsonString);

import 'dart:convert';

Hoteloffers hoteloffersFromJson(String str) => Hoteloffers.fromJson(json.decode(str));

String hoteloffersToJson(Hoteloffers data) => json.encode(data.toJson());

class Hoteloffers {
    Data? data;

    Hoteloffers({
        this.data,
    });

    factory Hoteloffers.fromJson(Map<String, dynamic> json) => Hoteloffers(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
    };
}

class Data {
    List<Offer>? offers;
    String? selectedCouponCode;

    Data({
        this.offers,
        this.selectedCouponCode,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        offers: json["offers"] == null ? [] : List<Offer>.from(json["offers"]!.map((x) => Offer.fromJson(x))),
        selectedCouponCode: json["selectedCouponCode"],
    );

    Map<String, dynamic> toJson() => {
        "offers": offers == null ? [] : List<dynamic>.from(offers!.map((x) => x.toJson())),
        "selectedCouponCode": selectedCouponCode,
    };
}

class Offer {
    String? couponCode;
    int? instantDiscount;
    int? displayedInstantDiscount;
    int? cashback;
    int? displayedCashback;
    BurnMoneyInfo? burnMoneyInfo;
    String? applyMessage;
    String? termsAndConditions;
    dynamic defaultFare;
    dynamic paymentOptionWrapper;
    PaymentType? paymentType;
    bool? duplicateCouponCodeInOffer;
    String? cashbackText;

    Offer({
        this.couponCode,
        this.instantDiscount,
        this.displayedInstantDiscount,
        this.cashback,
        this.displayedCashback,
        this.burnMoneyInfo,
        this.applyMessage,
        this.termsAndConditions,
        this.defaultFare,
        this.paymentOptionWrapper,
        this.paymentType,
        this.duplicateCouponCodeInOffer,
        this.cashbackText,
    });

    factory Offer.fromJson(Map<String, dynamic> json) => Offer(
        couponCode: json["couponCode"],
        instantDiscount: json["instantDiscount"],
        displayedInstantDiscount: json["displayedInstantDiscount"],
        cashback: json["cashback"],
        displayedCashback: json["displayedCashback"],
        burnMoneyInfo: json["burnMoneyInfo"] == null ? null : BurnMoneyInfo.fromJson(json["burnMoneyInfo"]),
        applyMessage: json["applyMessage"],
        termsAndConditions: json["termsAndConditions"],
        defaultFare: json["defaultFare"],
        paymentOptionWrapper: json["paymentOptionWrapper"],
        paymentType: paymentTypeValues.map[json["paymentType"]]!,
        duplicateCouponCodeInOffer: json["duplicateCouponCodeInOffer"],
        cashbackText: json["cashbackText"],
    );

    Map<String, dynamic> toJson() => {
        "couponCode": couponCode,
        "instantDiscount": instantDiscount,
        "displayedInstantDiscount": displayedInstantDiscount,
        "cashback": cashback,
        "displayedCashback": displayedCashback,
        "burnMoneyInfo": burnMoneyInfo?.toJson(),
        "applyMessage": applyMessage,
        "termsAndConditions": termsAndConditions,
        "defaultFare": defaultFare,
        "paymentOptionWrapper": paymentOptionWrapper,
        "paymentType": paymentTypeValues.reverse[paymentType],
        "duplicateCouponCodeInOffer": duplicateCouponCodeInOffer,
        "cashbackText": cashbackText,
    };
}

class BurnMoneyInfo {
    int? burnAmount;
    int? burnIxiMoneyAmount;
    int? burnIxiMoneyMaxAmount;
    Title? title;
    String? text;

    BurnMoneyInfo({
        this.burnAmount,
        this.burnIxiMoneyAmount,
        this.burnIxiMoneyMaxAmount,
        this.title,
        this.text,
    });

    factory BurnMoneyInfo.fromJson(Map<String, dynamic> json) => BurnMoneyInfo(
        burnAmount: json["burnAmount"],
        burnIxiMoneyAmount: json["burnIxiMoneyAmount"],
        burnIxiMoneyMaxAmount: json["burnIxiMoneyMaxAmount"],
        title: titleValues.map[json["title"]]!,
        text: json["text"],
    );

    Map<String, dynamic> toJson() => {
        "burnAmount": burnAmount,
        "burnIxiMoneyAmount": burnIxiMoneyAmount,
        "burnIxiMoneyMaxAmount": burnIxiMoneyMaxAmount,
        "title": titleValues.reverse[title],
        "text": text,
    };
}

enum Title {
    REDEEM_00_IXIGO_MONEY
}

final titleValues = EnumValues({
    "Redeem ₹0.0 ixigo money": Title.REDEEM_00_IXIGO_MONEY
});

enum PaymentType {
    BOOK_WITH_ZERO_PAYMENT,
    PAY_NOW
}

final paymentTypeValues = EnumValues({
    "BOOK_WITH_ZERO_PAYMENT": PaymentType.BOOK_WITH_ZERO_PAYMENT,
    "PAY_NOW": PaymentType.PAY_NOW
});

class EnumValues<T> {
    Map<String, T> map;
    late Map<T, String> reverseMap;

    EnumValues(this.map);

    Map<T, String> get reverse {
            reverseMap = map.map((k, v) => MapEntry(v, k));
            return reverseMap;
    }
}
