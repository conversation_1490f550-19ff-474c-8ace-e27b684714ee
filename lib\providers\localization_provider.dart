import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalizationProvider with ChangeNotifier {
  Locale _locale = const Locale('en');
  static const String _languageKey = 'selected_language';

  Locale get locale => _locale;

  // Check if current language is RTL
  bool get isRTL => _locale.languageCode == 'ar';

  // Get text direction based on current locale
  TextDirection get textDirection => isRTL ? TextDirection.rtl : TextDirection.ltr;

  LocalizationProvider() {
    _loadSavedLanguage();
  }

  // Load saved language from SharedPreferences
  Future<void> _loadSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString(_languageKey);
      if (languageCode != null) {
        _locale = Locale(languageCode);
        notifyListeners();
      }
    } catch (e) {
      // Handle error silently, keep default locale
      debugPrint('Error loading saved language: $e');
    }
  }

  // Save language to SharedPreferences
  Future<void> _saveLanguage(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
    } catch (e) {
      debugPrint('Error saving language: $e');
    }
  }

  // Set new locale and save it
  Future<void> setLocale(Locale locale) async {
    if (_locale == locale) return;
    
    _locale = locale;
    await _saveLanguage(locale.languageCode);
    notifyListeners();
  }

  // Get supported locales
  static List<Locale> get supportedLocales => const [
    Locale('en'), // English
    Locale('ar'), // Arabic
    Locale('es'), // Spanish
    Locale('fr'), // French
    Locale('hi'), // Hindi
  ];

  // Check if locale is supported
  static bool isSupported(Locale locale) {
    return supportedLocales.any((supportedLocale) => 
        supportedLocale.languageCode == locale.languageCode);
  }
}