import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/providers/home_provider.dart';
import 'package:provider/provider.dart';

class TouristPlacesDropdown extends StatelessWidget {
  const TouristPlacesDropdown({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Using Consumer to listen to changes in the HomeProvider
    return Consumer<HomeProvider>(
      builder: (context, provider, child) {
        return Container(
          decoration: BoxDecoration(
            boxShadow: [
              if (provider.destinationController.text.isNotEmpty)
                BoxShadow(
                  color: AppColors.primary.withAlpha(38),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
            ],
          ),
          child: TextField(
            controller: provider.destinationController,
            keyboardType: TextInputType.text,
            decoration: InputDecoration(
              labelStyle: TextStyle(color: AppColors.textLight),
              hintText: 'Where are you going?',
              prefixIcon: Icon(Icons.location_on_outlined, color: AppColors.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
              floatingLabelBehavior: FloatingLabelBehavior.auto,
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 1.5),
              ),
            ),
          ),
        );
      },
    );
  }
}
