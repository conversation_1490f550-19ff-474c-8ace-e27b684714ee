// To parse this JSON data, do
//
//     final hotelrooms = hotelroomsFromJson(jsonString);

import 'dart:convert';

Hotelrooms hotelroomsFromJson(String str) => Hotelrooms.fromJson(json.decode(str));

String hotelroomsToJson(Hotelrooms data) => json.encode(data.toJson());

class Hotelrooms {
    Data? data;

    Hotelrooms({
        this.data,
    });

    factory Hotelrooms.fromJson(Map<String, dynamic> json) => Hotelrooms(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
    };
}

class Data {
    HotelLevelDetails? hotelLevelDetails;
    List<Room>? rooms;
    Metadata? metadata;
    List<BenefitMap>? benefitMap;
    String? hotelInfoToken;
    int? providerId;

    Data({
        this.hotelLevelDetails,
        this.rooms,
        this.metadata,
        this.benefitMap,
        this.hotelInfoToken,
        this.providerId,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        hotelLevelDetails: json["hotelLevelDetails"] == null ? null : HotelLevelDetails.fromJson(json["hotelLevelDetails"]),
        rooms: json["rooms"] == null ? [] : List<Room>.from(json["rooms"]!.map((x) => Room.fromJson(x))),
        metadata: json["metadata"] == null ? null : Metadata.fromJson(json["metadata"]),
        benefitMap: json["benefitMap"] == null ? [] : List<BenefitMap>.from(json["benefitMap"]!.map((x) => BenefitMap.fromJson(x))),
        hotelInfoToken: json["hotelInfoToken"],
        providerId: json["providerId"],
    );

    Map<String, dynamic> toJson() => {
        "hotelLevelDetails": hotelLevelDetails?.toJson(),
        "rooms": rooms == null ? [] : List<dynamic>.from(rooms!.map((x) => x.toJson())),
        "metadata": metadata?.toJson(),
        "benefitMap": benefitMap == null ? [] : List<dynamic>.from(benefitMap!.map((x) => x.toJson())),
        "hotelInfoToken": hotelInfoToken,
        "providerId": providerId,
    };
}

class BenefitMap {
    String? name;
    String? code;
    List<String>? blockIds;
    int? count;

    BenefitMap({
        this.name,
        this.code,
        this.blockIds,
        this.count,
    });

    factory BenefitMap.fromJson(Map<String, dynamic> json) => BenefitMap(
        name: json["name"],
        code: json["code"],
        blockIds: json["blockIds"] == null ? [] : List<String>.from(json["blockIds"]!.map((x) => x)),
        count: json["count"],
    );

    Map<String, dynamic> toJson() => {
        "name": name,
        "code": code,
        "blockIds": blockIds == null ? [] : List<dynamic>.from(blockIds!.map((x) => x)),
        "count": count,
    };
}

class HotelLevelDetails {
    HotelDetail? hotelDetail;
    dynamic amenities;
    int? countryId;
    String? city;
    bool? isInternational;
    String? accommodationType;
    bool? bestPriceGuarantee;

    HotelLevelDetails({
        this.hotelDetail,
        this.amenities,
        this.countryId,
        this.city,
        this.isInternational,
        this.accommodationType,
        this.bestPriceGuarantee,
    });

    factory HotelLevelDetails.fromJson(Map<String, dynamic> json) => HotelLevelDetails(
        hotelDetail: json["hotelDetail"] == null ? null : HotelDetail.fromJson(json["hotelDetail"]),
        amenities: json["amenities"],
        countryId: json["countryId"],
        city: json["city"],
        isInternational: json["isInternational"],
        accommodationType: json["accommodationType"],
        bestPriceGuarantee: json["bestPriceGuarantee"],
    );

    Map<String, dynamic> toJson() => {
        "hotelDetail": hotelDetail?.toJson(),
        "amenities": amenities,
        "countryId": countryId,
        "city": city,
        "isInternational": isInternational,
        "accommodationType": accommodationType,
        "bestPriceGuarantee": bestPriceGuarantee,
    };
}

class HotelDetail {
    String? hotelName;
    String? thumbnailImageUrl;

    HotelDetail({
        this.hotelName,
        this.thumbnailImageUrl,
    });

    factory HotelDetail.fromJson(Map<String, dynamic> json) => HotelDetail(
        hotelName: json["hotelName"],
        thumbnailImageUrl: json["thumbnailImageURL"],
    );

    Map<String, dynamic> toJson() => {
        "hotelName": hotelName,
        "thumbnailImageURL": thumbnailImageUrl,
    };
}

class Metadata {
    String? rateMethod;
    RoomTypeMapping? roomTypeMapping;

    Metadata({
        this.rateMethod,
        this.roomTypeMapping,
    });

    factory Metadata.fromJson(Map<String, dynamic> json) => Metadata(
        rateMethod: json["rateMethod"],
        roomTypeMapping: json["roomTypeMapping"] == null ? null : RoomTypeMapping.fromJson(json["roomTypeMapping"]),
    );

    Map<String, dynamic> toJson() => {
        "rateMethod": rateMethod,
        "roomTypeMapping": roomTypeMapping?.toJson(),
    };
}

class RoomTypeMapping {
    String? standardRoom;
    String? deluxeRoom;
    String? suite;

    RoomTypeMapping({
        this.standardRoom,
        this.deluxeRoom,
        this.suite,
    });

    factory RoomTypeMapping.fromJson(Map<String, dynamic> json) => RoomTypeMapping(
        standardRoom: json["Standard Room"],
        deluxeRoom: json["Deluxe Room"],
        suite: json["Suite"],
    );

    Map<String, dynamic> toJson() => {
        "Standard Room": standardRoom,
        "Deluxe Room": deluxeRoom,
        "Suite": suite,
    };
}

class Room {
    String? id;
    String? masterRoomId;
    String? name;
    dynamic extraBedText;
    int? roomsLeft;
    List<Property>? properties;
    List<String>? roomLevelAmenities;
    List<RoomOption>? roomOptions;
    List<ImageList>? imageList;
    int? providerId;
    FareDetail? fareDetail;

    Room({
        this.id,
        this.masterRoomId,
        this.name,
        this.extraBedText,
        this.roomsLeft,
        this.properties,
        this.roomLevelAmenities,
        this.roomOptions,
        this.imageList,
        this.providerId,
        this.fareDetail,
    });

    factory Room.fromJson(Map<String, dynamic> json) => Room(
        id: json["id"],
        masterRoomId: json["masterRoomId"],
        name: json["name"],
        extraBedText: json["extraBedText"],
        roomsLeft: json["roomsLeft"],
        properties: json["properties"] == null ? [] : List<Property>.from(json["properties"]!.map((x) => Property.fromJson(x))),
        roomLevelAmenities: json["roomLevelAmenities"] == null ? [] : List<String>.from(json["roomLevelAmenities"]!.map((x) => x)),
        roomOptions: json["roomOptions"] == null ? [] : List<RoomOption>.from(json["roomOptions"]!.map((x) => RoomOption.fromJson(x))),
        imageList: json["imageList"] == null ? [] : List<ImageList>.from(json["imageList"]!.map((x) => ImageList.fromJson(x))),
        providerId: json["providerId"],
        fareDetail: json["fareDetail"] == null ? null : FareDetail.fromJson(json["fareDetail"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "masterRoomId": masterRoomId,
        "name": name,
        "extraBedText": extraBedText,
        "roomsLeft": roomsLeft,
        "properties": properties == null ? [] : List<dynamic>.from(properties!.map((x) => x.toJson())),
        "roomLevelAmenities": roomLevelAmenities == null ? [] : List<dynamic>.from(roomLevelAmenities!.map((x) => x)),
        "roomOptions": roomOptions == null ? [] : List<dynamic>.from(roomOptions!.map((x) => x.toJson())),
        "imageList": imageList == null ? [] : List<dynamic>.from(imageList!.map((x) => x.toJson())),
        "providerId": providerId,
        "fareDetail": fareDetail?.toJson(),
    };
}

class FareDetail {
    int? displayedBaseFare;
    double? baseFare;
    int? markUpFare;
    String? markupDiscountPercent;
    double? taxesAndFees;
    double? totalPrice;
    dynamic burnMoneyInfo;
    String? couponCode;
    dynamic cashback;
    dynamic cashbackText;
    double? instantDiscount;
    double? totalDiscount;
    double? totalPgAmount;
    double? totalBookingAmount;
    bool? bankOfferTextIncluded;
    ExcludedChargeMap? offerText;
    dynamic offerMessage;
    TaxAndChargeMap? taxAndChargeMap;
    DiscountMap? discountMap;
    ExcludedChargeMap? excludedChargeMap;
    String? discountViewType;
    String? method;
    double? totalBaseFare;
    double? totalTaxesAndFees;
    double? convenienceFee;
    double? totalConvenienceFee;
    String? paymentType;
    dynamic payAtHotelAmount;
    double? offset;
    int? numberOfPersons;

    FareDetail({
        this.displayedBaseFare,
        this.baseFare,
        this.markUpFare,
        this.markupDiscountPercent,
        this.taxesAndFees,
        this.totalPrice,
        this.burnMoneyInfo,
        this.couponCode,
        this.cashback,
        this.cashbackText,
        this.instantDiscount,
        this.totalDiscount,
        this.totalPgAmount,
        this.totalBookingAmount,
        this.bankOfferTextIncluded,
        this.offerText,
        this.offerMessage,
        this.taxAndChargeMap,
        this.discountMap,
        this.excludedChargeMap,
        this.discountViewType,
        this.method,
        this.totalBaseFare,
        this.totalTaxesAndFees,
        this.convenienceFee,
        this.totalConvenienceFee,
        this.paymentType,
        this.payAtHotelAmount,
        this.offset,
        this.numberOfPersons,
    });

    factory FareDetail.fromJson(Map<String, dynamic> json) => FareDetail(
        displayedBaseFare: json["displayedBaseFare"],
        baseFare: json["baseFare"]?.toDouble(),
        markUpFare: json["markUpFare"],
        markupDiscountPercent: json["markupDiscountPercent"],
        taxesAndFees: json["taxesAndFees"]?.toDouble(),
        totalPrice: json["totalPrice"]?.toDouble(),
        burnMoneyInfo: json["burnMoneyInfo"],
        couponCode: json["couponCode"],
        cashback: json["cashback"],
        cashbackText: json["cashbackText"],
        instantDiscount: json["instantDiscount"]?.toDouble(),
        totalDiscount: json["totalDiscount"]?.toDouble(),
        totalPgAmount: json["totalPGAmount"]?.toDouble(),
        totalBookingAmount: json["totalBookingAmount"]?.toDouble(),
        bankOfferTextIncluded: json["bankOfferTextIncluded"],
        offerText: json["offerText"] == null ? null : ExcludedChargeMap.fromJson(json["offerText"]),
        offerMessage: json["offerMessage"],
        taxAndChargeMap: json["taxAndChargeMap"] == null ? null : TaxAndChargeMap.fromJson(json["taxAndChargeMap"]),
        discountMap: json["discountMap"] == null ? null : DiscountMap.fromJson(json["discountMap"]),
        excludedChargeMap: json["excludedChargeMap"] == null ? null : ExcludedChargeMap.fromJson(json["excludedChargeMap"]),
        discountViewType: json["discountViewType"],
        method: json["method"],
        totalBaseFare: json["totalBaseFare"]?.toDouble(),
        totalTaxesAndFees: json["totalTaxesAndFees"]?.toDouble(),
        convenienceFee: json["convenienceFee"]?.toDouble(),
        totalConvenienceFee: json["totalConvenienceFee"]?.toDouble(),
        paymentType: json["paymentType"],
        payAtHotelAmount: json["payAtHotelAmount"],
        offset: json["offset"]?.toDouble(),
        numberOfPersons: json["numberOfPersons"],
    );

    Map<String, dynamic> toJson() => {
        "displayedBaseFare": displayedBaseFare,
        "baseFare": baseFare,
        "markUpFare": markUpFare,
        "markupDiscountPercent": markupDiscountPercent,
        "taxesAndFees": taxesAndFees,
        "totalPrice": totalPrice,
        "burnMoneyInfo": burnMoneyInfo,
        "couponCode": couponCode,
        "cashback": cashback,
        "cashbackText": cashbackText,
        "instantDiscount": instantDiscount,
        "totalDiscount": totalDiscount,
        "totalPGAmount": totalPgAmount,
        "totalBookingAmount": totalBookingAmount,
        "bankOfferTextIncluded": bankOfferTextIncluded,
        "offerText": offerText?.toJson(),
        "offerMessage": offerMessage,
        "taxAndChargeMap": taxAndChargeMap?.toJson(),
        "discountMap": discountMap?.toJson(),
        "excludedChargeMap": excludedChargeMap?.toJson(),
        "discountViewType": discountViewType,
        "method": method,
        "totalBaseFare": totalBaseFare,
        "totalTaxesAndFees": totalTaxesAndFees,
        "convenienceFee": convenienceFee,
        "totalConvenienceFee": totalConvenienceFee,
        "paymentType": paymentType,
        "payAtHotelAmount": payAtHotelAmount,
        "offset": offset,
        "numberOfPersons": numberOfPersons,
    };
}

class DiscountMap {
    double? reversalOfConvenienceFee;
    double? couponDiscount;
    double? supplierDiscount;

    DiscountMap({
        this.reversalOfConvenienceFee,
        this.couponDiscount,
        this.supplierDiscount,
    });

    factory DiscountMap.fromJson(Map<String, dynamic> json) => DiscountMap(
        reversalOfConvenienceFee: json["Reversal of Convenience fee"]?.toDouble(),
        couponDiscount: json["Coupon Discount"]?.toDouble(),
        supplierDiscount: json["Supplier Discount"]?.toDouble(),
    );

    Map<String, dynamic> toJson() => {
        "Reversal of Convenience fee": reversalOfConvenienceFee,
        "Coupon Discount": couponDiscount,
        "Supplier Discount": supplierDiscount,
    };
}

class ExcludedChargeMap {
    ExcludedChargeMap();

    factory ExcludedChargeMap.fromJson(Map<String, dynamic> json) => ExcludedChargeMap(
    );

    Map<String, dynamic> toJson() => {
    };
}

class TaxAndChargeMap {
    double? hotelTax;
    double? convenienceFee;

    TaxAndChargeMap({
        this.hotelTax,
        this.convenienceFee,
    });

    factory TaxAndChargeMap.fromJson(Map<String, dynamic> json) => TaxAndChargeMap(
        hotelTax: json["Hotel Tax"]?.toDouble(),
        convenienceFee: json["Convenience Fee"]?.toDouble(),
    );

    Map<String, dynamic> toJson() => {
        "Hotel Tax": hotelTax,
        "Convenience Fee": convenienceFee,
    };
}

class ImageList {
    String? url;
    Caption? caption;

    ImageList({
        this.url,
        this.caption,
    });

    factory ImageList.fromJson(Map<String, dynamic> json) => ImageList(
        url: json["url"],
        caption: captionValues.map[json["caption"]]!,
    );

    Map<String, dynamic> toJson() => {
        "url": url,
        "caption": captionValues.reverse[caption],
    };
}

enum Caption {
    BED,
    EMPTY,
    ROOM_PLAN,
    VIEW
}

final captionValues = EnumValues({
    "Bed": Caption.BED,
    "": Caption.EMPTY,
    "Room plan": Caption.ROOM_PLAN,
    "View": Caption.VIEW
});

class Property {
    String? code;
    String? data;

    Property({
        this.code,
        this.data,
    });

    factory Property.fromJson(Map<String, dynamic> json) => Property(
        code: json["code"],
        data: json["data"],
    );

    Map<String, dynamic> toJson() => {
        "code": code,
        "data": data,
    };
}

class RoomOption {
    String? blockId;
    String? name;
    List<String>? mealBenefits;
    CancellationBenefits? cancellationBenefits;
    int? extraBenefitsCount;
    List<String>? otherBenefits;
    int? priceIncrease;
    String? fomoTag;
    bool? isRecommended;
    String? paymentType;
    int? roomIndex;
    FareDetail? fareDetail;

    RoomOption({
        this.blockId,
        this.name,
        this.mealBenefits,
        this.cancellationBenefits,
        this.extraBenefitsCount,
        this.otherBenefits,
        this.priceIncrease,
        this.fomoTag,
        this.isRecommended,
        this.paymentType,
        this.roomIndex,
        this.fareDetail,
    });

    factory RoomOption.fromJson(Map<String, dynamic> json) => RoomOption(
        blockId: json["blockId"],
        name: json["name"],
        mealBenefits: json["mealBenefits"] == null ? [] : List<String>.from(json["mealBenefits"]!.map((x) => x)),
        cancellationBenefits: json["cancellationBenefits"] == null ? null : CancellationBenefits.fromJson(json["cancellationBenefits"]),
        extraBenefitsCount: json["extraBenefitsCount"],
        otherBenefits: json["otherBenefits"] == null ? [] : List<String>.from(json["otherBenefits"]!.map((x) => x)),
        priceIncrease: json["priceIncrease"],
        fomoTag: json["fomoTag"],
        isRecommended: json["isRecommended"],
        paymentType: json["paymentType"],
        roomIndex: json["roomIndex"],
        fareDetail: json["fareDetail"] == null ? null : FareDetail.fromJson(json["fareDetail"]),
    );

    Map<String, dynamic> toJson() => {
        "blockId": blockId,
        "name": name,
        "mealBenefits": mealBenefits == null ? [] : List<dynamic>.from(mealBenefits!.map((x) => x)),
        "cancellationBenefits": cancellationBenefits?.toJson(),
        "extraBenefitsCount": extraBenefitsCount,
        "otherBenefits": otherBenefits == null ? [] : List<dynamic>.from(otherBenefits!.map((x) => x)),
        "priceIncrease": priceIncrease,
        "fomoTag": fomoTag,
        "isRecommended": isRecommended,
        "paymentType": paymentType,
        "roomIndex": roomIndex,
        "fareDetail": fareDetail?.toJson(),
    };
}

class CancellationBenefits {
    Code? code;
    String? data;
    List<Policy>? policy;
    Remarks? remarks;

    CancellationBenefits({
        this.code,
        this.data,
        this.policy,
        this.remarks,
    });

    factory CancellationBenefits.fromJson(Map<String, dynamic> json) => CancellationBenefits(
        code: codeValues.map[json["code"]]!,
        data: json["data"],
        policy: json["policy"] == null ? [] : List<Policy>.from(json["policy"]!.map((x) => Policy.fromJson(x))),
        remarks: json["remarks"] == null ? null : Remarks.fromJson(json["remarks"]),
    );

    Map<String, dynamic> toJson() => {
        "code": codeValues.reverse[code],
        "data": data,
        "policy": policy == null ? [] : List<dynamic>.from(policy!.map((x) => x.toJson())),
        "remarks": remarks?.toJson(),
    };
}

enum Code {
    FULLY_REFUNDABLE,
    NON_REFUNDABLE
}

final codeValues = EnumValues({
    "FULLY_REFUNDABLE": Code.FULLY_REFUNDABLE,
    "NON_REFUNDABLE": Code.NON_REFUNDABLE
});

class Policy {
    Code? code;
    String? title;
    String? subTitle;
    DateTime? cancellationDate;
    DateTime? checkInDate;
    DateTime? startDate;
    DateTime? endDate;
    double? cancellationAmount;

    Policy({
        this.code,
        this.title,
        this.subTitle,
        this.cancellationDate,
        this.checkInDate,
        this.startDate,
        this.endDate,
        this.cancellationAmount,
    });

    factory Policy.fromJson(Map<String, dynamic> json) => Policy(
        code: codeValues.map[json["code"]]!,
        title: json["title"],
        subTitle: json["subTitle"],
        cancellationDate: json["cancellationDate"] == null ? null : DateTime.parse(json["cancellationDate"]),
        checkInDate: json["checkInDate"] == null ? null : DateTime.parse(json["checkInDate"]),
        startDate: json["startDate"] == null ? null : DateTime.parse(json["startDate"]),
        endDate: json["endDate"] == null ? null : DateTime.parse(json["endDate"]),
        cancellationAmount: json["cancellationAmount"]?.toDouble(),
    );

    Map<String, dynamic> toJson() => {
        "code": codeValues.reverse[code],
        "title": title,
        "subTitle": subTitle,
        "cancellationDate": cancellationDate?.toIso8601String(),
        "checkInDate": checkInDate?.toIso8601String(),
        "startDate": startDate?.toIso8601String(),
        "endDate": endDate?.toIso8601String(),
        "cancellationAmount": cancellationAmount,
    };
}

class Remarks {
    String? title;
    dynamic text;
    String? subText;

    Remarks({
        this.title,
        this.text,
        this.subText,
    });

    factory Remarks.fromJson(Map<String, dynamic> json) => Remarks(
        title: json["title"],
        text: json["text"],
        subText: json["subText"],
    );

    Map<String, dynamic> toJson() => {
        "title": title,
        "text": text,
        "subText": subText,
    };
}

class EnumValues<T> {
    Map<String, T> map;
    late Map<T, String> reverseMap;

    EnumValues(this.map);

    Map<T, String> get reverse {
            reverseMap = map.map((k, v) => MapEntry(v, k));
            return reverseMap;
    }
}
