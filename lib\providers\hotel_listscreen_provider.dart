import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hotel_booking/models/hotel_details.dart';

class HotelListProvider with ChangeNotifier {
  // Search parameters
  String _destination = '';
  DateTime? _checkInDate;
  DateTime? _checkOutDate;
  int _guests = 1;

  // UI state
  bool _isLoading = false;
  String? _error;
  bool _isFilterOpen = false;
  String _sortOption = 'popularity'; // Default sort option

  // Hotel data
  List<InventoryInfoList> _hotels = [];
  List<InventoryInfoList> _filteredHotels = [];

  // Getters - Basic search parameters
  String get destination => _destination;
  DateTime? get checkInDate => _checkInDate;
  DateTime? get checkOutDate => _checkOutDate;
  int get guests => _guests;

  // Getters - UI state
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isFilterOpen => _isFilterOpen;
  String get sortOption => _sortOption;

  // Getters - Hotel data
  List<InventoryInfoList> get hotels => _filteredHotels;
  bool get hasHotels => _filteredHotels.isNotEmpty;

  // Constructor with initialization
  HotelListProvider() {
    _initializeDefaults();
    loadHotels();
  }

  // Initialize default values
  void _initializeDefaults() {
    _checkInDate = DateTime.now();
    _checkOutDate = DateTime.now().add(const Duration(days: 3));
    _guests = 1;
  }

  // Load hotels data
  Future<void> loadHotels() async {
    _setLoading(true);
    _error = null;

    try {
      final String response =
          await rootBundle.loadString('assets/json/hotels.json');
      final Map<String, dynamic> jsonData = json.decode(response);

// Navigate to inventoryInfoList
      final List<dynamic>? hotelsData =
          jsonData['data']?['result']?['inventoryInfoList'];

      if (hotelsData != null) {
        _hotels =
            hotelsData.map((json) => InventoryInfoList.fromJson(json)).toList();
        _applyFilters();
        notifyListeners();
      } else {
        throw Exception('No hotel data found in JSON');
      }
    } catch (e) {
      print('Error loading hotels: $e');
      _error = 'Failed to load hotels. Please try again.';
    } finally {
      _setLoading(false);
    }
  }

  // Apply filters to hotel list
  void _applyFilters() {
    // Debug print to check if hotels are loaded
    print('Total hotels loaded: ${_hotels.length}');

    // First, assign all hotels to filtered hotels (for debugging)
    _filteredHotels = List.from(_hotels);

    // Debug print to check hotel locations
    for (var hotel in _hotels) {
      print(
          'Hotel: ${hotel.name}, City: ${hotel.locality}, State: ${hotel.city}');
    }

    // Now apply the actual filtering
    _filteredHotels = _hotels.where((hotel) {
      // Filter by destination (city or state)
      if (_destination.isNotEmpty && hotel.locality != null) {
        final cityMatch = hotel.locality != null &&
            hotel.locality!.toLowerCase().contains(_destination.toLowerCase());
        final stateMatch = hotel.city != null &&
            hotel.city!.toLowerCase().contains(_destination.toLowerCase());

        print(
            'Hotel: ${hotel.name}, Destination: $_destination, City Match: $cityMatch, State Match: $stateMatch');

        if (!cityMatch && !stateMatch) {
          return false;
        }
      }

      return true;
    }).toList();

    print('Filtered hotels count: ${_filteredHotels.length}');

    // Apply sorting based on selected option
    switch (_sortOption) {
      case 'rating':
        _filteredHotels
            .sort((a, b) => (b.starRating ?? 0).compareTo(a.starRating ?? 0));
        break;
      case 'price_desc':
        _filteredHotels.sort((a, b) => (b.fareDetail?.totalPrice ?? 0)
            .compareTo(a.fareDetail?.totalPrice ?? 0));
        break;
      case 'price_asc':
        _filteredHotels.sort((a, b) => (a.fareDetail?.totalPrice ?? 0)
            .compareTo(b.fareDetail?.totalPrice ?? 0));
        break;
      case 'popularity':
      default:
        _filteredHotels.sort((a, b) =>
            (b.userRatingCount ?? 0).compareTo(a.userRatingCount ?? 0));
        break;
    }
  }

  // Helper method to set loading state
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  // Search parameters setters
  void setDestination(String destination) {
    if (_destination == destination) return;
    _destination = destination;
    notifyListeners();
  }

  void setCheckInDate(DateTime? date) {
    if (_checkInDate == date) return;
    _checkInDate = date;

    // If check-out date is before check-in date, update it
    if (_checkOutDate != null &&
        _checkInDate != null &&
        _checkOutDate!.isBefore(_checkInDate!)) {
      _checkOutDate = _checkInDate!.add(const Duration(days: 1));
    }

    notifyListeners();
  }

  void setCheckOutDate(DateTime? date) {
    if (_checkOutDate == date) return;
    _checkOutDate = date;

    // If check-in date is after check-out date, update it
    if (_checkInDate != null &&
        _checkOutDate != null &&
        _checkInDate!.isAfter(_checkOutDate!)) {
      _checkInDate = _checkOutDate!.subtract(const Duration(days: 1));
    }

    notifyListeners();
  }

  void setGuests(int guests) {
    if (_guests == guests) return;
    _guests = guests;
    notifyListeners();
  }

  // Filter and sort methods
  void toggleFilter() {
    _isFilterOpen = !_isFilterOpen;
    notifyListeners();
  }

  void setSortOption(String option) {
    if (_sortOption == option) return;
    _sortOption = option;
    _applyFilters();
    notifyListeners();
  }

  // Format date helper
  String formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Retry loading hotels
  Future<void> retryLoadingHotels() async {
    await loadHotels();
  }
}
