import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:hotel_booking/providers/home_provider.dart';
import 'package:hotel_booking/constants/app_colors.dart';

class DateSelectionWidget extends StatefulWidget {
  const DateSelectionWidget({super.key});

  @override
  State<DateSelectionWidget> createState() => _DateSelectionWidgetState();
}

class _DateSelectionWidgetState extends State<DateSelectionWidget> {
  bool isSelectingCheckIn = true;
  final DateFormat dateFormat = DateFormat('MMM dd, yyyy');
  final DateFormat dayFormat = DateFormat('dd');
  final DateFormat monthYearFormat = DateFormat('MMMM yyyy');
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    
    // Set default dates if they're not already set
    final provider = Provider.of<HomeProvider>(context, listen: false);
    if (provider.checkInDate == null || provider.checkOutDate == null) {
      // Set default check-in date to today
      provider.setCheckInDate(DateTime.now());
      
      // Set default check-out date to tomorrow
      provider.setCheckOutDate(DateTime.now().add(const Duration(days: 1)));
    }
    
    // Add a small delay to ensure the UI is built before scrolling
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Scroll to the month of the selected date or current month
      DateTime targetDate = isSelectingCheckIn && provider.checkInDate != null 
          ? provider.checkInDate! 
          : !isSelectingCheckIn && provider.checkOutDate != null
              ? provider.checkOutDate!
              : DateTime.now();
      
      // Calculate approximate position to scroll to
      int monthsFromCurrent = (targetDate.year - DateTime.now().year) * 12 + 
                              (targetDate.month - DateTime.now().month);
      
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          monthsFromCurrent * 300.0, // Approximate height of a month view
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _selectDate(DateTime date, HomeProvider provider) {
    if (isSelectingCheckIn) {
      // Selecting check-in date
      provider.setCheckInDate(date);
      
      // If new check-in date is after current check-out date or check-out not set,
      // set check-out to the day after check-in
      if (provider.checkOutDate == null || 
          date.isAfter(provider.checkOutDate!) || 
          isSameDay(date, provider.checkOutDate!)) {
        provider.setCheckOutDate(
          DateTime(date.year, date.month, date.day + 1)
        );
      }
      
      // Move to selecting check-out
      setState(() {
        isSelectingCheckIn = false;
      });
    } else {
      // Selecting check-out date
      if (date.isBefore(provider.checkInDate!) || isSameDay(date, provider.checkInDate!)) {
        // If selected date is before or same as check-in, swap them
        DateTime tempDate = provider.checkInDate!;
        provider.setCheckInDate(date);
        provider.setCheckOutDate(
          DateTime(tempDate.year, tempDate.month, tempDate.day)
        );
      } else {
        provider.setCheckOutDate(date);
      }
      
      // Stay in check-out selection mode
      setState(() {
        isSelectingCheckIn = false;
      });
    }
  }
  
  // Helper to check if two dates are the same day
  bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && 
           date1.month == date2.month && 
           date1.day == date2.day;
  }

  bool _isDateInRange(DateTime date, HomeProvider provider) {
    if (provider.checkInDate == null || provider.checkOutDate == null) return false;
    return date.isAfter(provider.checkInDate!) && 
           date.isBefore(provider.checkOutDate!);
  }

  bool _isCheckInDate(DateTime date, HomeProvider provider) {
    if (provider.checkInDate == null) return false;
    return isSameDay(date, provider.checkInDate!);
  }

  bool _isCheckOutDate(DateTime date, HomeProvider provider) {
    if (provider.checkOutDate == null) return false;
    return isSameDay(date, provider.checkOutDate!);
  }

  bool _isSelectable(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    return !date.isBefore(today);
  }

  Widget _buildMonthCalendar(DateTime month, HomeProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            monthYearFormat.format(month),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        // Days of week header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: const ['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day) {
            return SizedBox(
              width: 40,
              child: Center(
                child: Text(
                  day,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white70,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        // Calendar grid
        _buildMonthDays(month, provider),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildMonthDays(DateTime month, HomeProvider provider) {
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    final firstDayOfMonth = DateTime(month.year, month.month, 1);
    final firstWeekdayOfMonth = firstDayOfMonth.weekday; // 1-7 (Monday-Sunday)
    
    // Adjust to Sunday-based index (0-6)
    final firstDayIndex = firstWeekdayOfMonth % 7;
    
    // Calculate rows needed (including partial first and last week)
    final numRows = ((daysInMonth + firstDayIndex) / 7).ceil();
    
    return Column(
      children: List.generate(numRows, (rowIndex) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(7, (colIndex) {
            final dayIndex = rowIndex * 7 + colIndex - firstDayIndex;
            if (dayIndex < 0 || dayIndex >= daysInMonth) {
              // Empty cell
              return const SizedBox(width: 40, height: 40);
            }
            
            final date = DateTime(month.year, month.month, dayIndex + 1);
            final isSelectable = _isSelectable(date);
            final isCheckIn = _isCheckInDate(date, provider);
            final isCheckOut = _isCheckOutDate(date, provider);
            final isInRange = _isDateInRange(date, provider);
            
            return GestureDetector(
              onTap: () {
                if (isSelectable) {
                  _selectDate(date, provider);
                  setState(() {}); // Refresh the UI
                }
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isCheckIn || isCheckOut
                      ? AppColors.secondary
                      : isInRange
                          ? AppColors.secondary.withOpacity(0.2)
                          : Colors.transparent,
                  borderRadius: (isCheckIn || isCheckOut) && !isInRange
                      ? null  // No borderRadius when shape is circle
                      : isCheckIn
                          ? const BorderRadius.horizontal(left: Radius.circular(20))
                          : isCheckOut
                              ? const BorderRadius.horizontal(right: Radius.circular(20))
                              : isInRange
                                  ? BorderRadius.zero
                                  : BorderRadius.circular(20),
                  shape: (isCheckIn || isCheckOut) && !isInRange
                      ? BoxShape.circle
                      : BoxShape.rectangle,
                ),
                alignment: Alignment.center,
                child: Text(
                  dayFormat.format(date),
                  style: TextStyle(
                    color: isCheckIn || isCheckOut
                        ? Colors.white
                        : !isSelectable
                            ? Colors.grey.shade400
                            : isInRange
                                ? AppColors.secondary
                                : Colors.white,
                    fontWeight: isCheckIn || isCheckOut ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            );
          }),
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<HomeProvider>(
      builder: (context, provider, child) {
        return Container(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Select Dates',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Date selection section
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          isSelectingCheckIn = true;
                          
                          // Scroll to check-in date month
                          if (provider.checkInDate != null && _scrollController.hasClients) {
                            int monthsFromCurrent = (provider.checkInDate!.year - DateTime.now().year) * 12 + 
                                                   (provider.checkInDate!.month - DateTime.now().month);
                            
                            _scrollController.animateTo(
                              monthsFromCurrent * 300.0, 
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelectingCheckIn ? AppColors.secondary : Colors.grey.shade300,
                            width: isSelectingCheckIn ? 2.0 : 1.0,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'CHECK-IN',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              provider.checkInDate != null
                                  ? dateFormat.format(provider.checkInDate!)
                                  : 'Select date',
                              style: TextStyle(
                                fontSize: 16,
                                color: provider.checkInDate != null ? Colors.black : Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {                        
                        setState(() {
                          // If check-in not selected yet, set it to today first
                          if (provider.checkInDate == null) {
                            provider.setCheckInDate(DateTime.now());
                          }
                          
                          isSelectingCheckIn = false;
                          
                          // Scroll to check-out date month if selected, otherwise check-in date month
                          DateTime targetDate = provider.checkOutDate ?? provider.checkInDate!;
                          if (_scrollController.hasClients) {
                            int monthsFromCurrent = (targetDate.year - DateTime.now().year) * 12 + 
                                                   (targetDate.month - DateTime.now().month);
                            
                            _scrollController.animateTo(
                              monthsFromCurrent * 300.0, 
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: !isSelectingCheckIn ? AppColors.secondary : Colors.grey.shade300,
                            width: !isSelectingCheckIn ? 2.0 : 1.0,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'CHECK-OUT',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              provider.checkOutDate != null
                                  ? dateFormat.format(provider.checkOutDate!)
                                  : 'Select date',
                              style: TextStyle(
                                fontSize: 16,
                                color: provider.checkOutDate != null ? Colors.black : Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              // Show calendar
              Flexible(
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                    children: List.generate(12, (monthIndex) {
                      final currentDate = DateTime.now();
                      final month = DateTime(currentDate.year, currentDate.month + monthIndex);
                      return _buildMonthCalendar(month, provider);
                    }),
                  ),
                ),
              ),
              // Show selected nights if both dates selected
              if (provider.checkInDate != null && provider.checkOutDate != null) ...[
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.secondary.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.secondary.withOpacity(0.5)),
                  ),
                  child: Text(
                    '${provider.checkOutDate!.difference(provider.checkInDate!).inDays} nights selected',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Done button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.secondary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      'Apply',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}