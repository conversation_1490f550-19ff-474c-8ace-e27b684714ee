import 'package:flutter/material.dart';
import 'package:hotel_booking/models/hotel_details.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class WishlistProvider with ChangeNotifier {
  List<InventoryInfoList> _wishlistItems = [];
  bool _isLoading = false;

  List<InventoryInfoList> get wishlistItems => _wishlistItems;
  bool get isLoading => _isLoading;
  bool get isEmpty => _wishlistItems.isEmpty;
  int get itemCount => _wishlistItems.length;

  WishlistProvider() {
    _loadWishlistFromStorage();
  }

  // Check if a hotel is in the wishlist
  bool isInWishlist(String hotelId) {
    return _wishlistItems.any((hotel) => hotel.hotelId?.toString() == hotelId);
  }

  // Add hotel to wishlist
  Future<void> addToWishlist(InventoryInfoList hotel) async {
    if (!isInWishlist(hotel.hotelId?.toString() ?? '')) {
      _wishlistItems.add(hotel);
      await _saveWishlistToStorage();
      notifyListeners();
    }
  }

  // Remove hotel from wishlist
  Future<void> removeFromWishlist(String hotelId) async {
    _wishlistItems.removeWhere((hotel) => hotel.hotelId?.toString() == hotelId);
    await _saveWishlistToStorage();
    notifyListeners();
  }

  // Toggle wishlist status
  Future<void> toggleWishlist(InventoryInfoList hotel) async {
    if (isInWishlist(hotel.hotelId?.toString() ?? '')) {
      await removeFromWishlist(hotel.hotelId?.toString() ?? '');
    } else {
      await addToWishlist(hotel);
    }
  }

  // Clear all wishlist items
  Future<void> clearWishlist() async {
    _wishlistItems.clear();
    await _saveWishlistToStorage();
    notifyListeners();
  }

  // Load wishlist from SharedPreferences
  Future<void> _loadWishlistFromStorage() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final String? wishlistJson = prefs.getString('wishlist_items');

      if (wishlistJson != null) {
        final List<dynamic> decodedList = json.decode(wishlistJson);
        _wishlistItems = decodedList
            .map((item) => InventoryInfoList.fromJson(item))
            .toList();
      }
    } catch (e) {
      debugPrint('Error loading wishlist from storage: $e');
      _wishlistItems = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Save wishlist to SharedPreferences
  Future<void> _saveWishlistToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<Map<String, dynamic>> wishlistJson =
          _wishlistItems.map((hotel) => hotel.toJson()).toList();
      await prefs.setString('wishlist_items', json.encode(wishlistJson));
    } catch (e) {
      debugPrint('Error saving wishlist to storage: $e');
    }
  }

  // Get wishlist item by hotel ID
  InventoryInfoList? getWishlistItem(String hotelId) {
    try {
      return _wishlistItems.firstWhere((hotel) => hotel.hotelId?.toString() == hotelId);
    } catch (e) {
      return null;
    }
  }

  // Get wishlist items as a formatted list for display
  List<Map<String, dynamic>> getFormattedWishlistItems() {
    return _wishlistItems.map((hotel) {
      return {
        'id': hotel.hotelId?.toString() ?? '',
        'name': hotel.name ?? 'Unknown Hotel',
        'location': '${hotel.locality ?? ''}, ${hotel.city ?? ''}',
        'rating': hotel.starRating ?? 0.0,
        'price': hotel.fareDetail?.totalPrice ?? 0,
        'image': hotel.imageInfoList?.isNotEmpty == true
            ? hotel.imageInfoList!.first.url ?? ''
            : 'https://images.unsplash.com/photo-1566073771259-6a8506099945',
        'amenities': hotel.amenities?.map((amenity) => amenity.name ?? '').toList() ?? [],
        'userRating': hotel.userRating ?? 0.0,
        'userRatingCount': hotel.userRatingCount ?? 0,
        'hotel': hotel, // Include the full hotel object for navigation
      };
    }).toList();
  }

  // Search wishlist items
  List<InventoryInfoList> searchWishlist(String query) {
    if (query.isEmpty) return _wishlistItems;

    return _wishlistItems.where((hotel) {
      final name = hotel.name?.toLowerCase() ?? '';
      final city = hotel.city?.toLowerCase() ?? '';
      final locality = hotel.locality?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();

      return name.contains(searchQuery) ||
             city.contains(searchQuery) ||
             locality.contains(searchQuery);
    }).toList();
  }

  // Sort wishlist items
  void sortWishlist(String sortBy) {
    switch (sortBy) {
      case 'name':
        _wishlistItems.sort((a, b) =>
            (a.name ?? '').compareTo(b.name ?? ''));
        break;
      case 'price_low':
        _wishlistItems.sort((a, b) =>
            (a.fareDetail?.totalPrice ?? 0).compareTo(b.fareDetail?.totalPrice ?? 0));
        break;
      case 'price_high':
        _wishlistItems.sort((a, b) =>
            (b.fareDetail?.totalPrice ?? 0).compareTo(a.fareDetail?.totalPrice ?? 0));
        break;
      case 'rating':
        _wishlistItems.sort((a, b) =>
            (b.starRating ?? 0).compareTo(a.starRating ?? 0));
        break;
      default:
        // Keep original order
        break;
    }
    notifyListeners();
  }

  // Get statistics
  Map<String, dynamic> getWishlistStats() {
    if (_wishlistItems.isEmpty) {
      return {
        'totalItems': 0,
        'averagePrice': 0.0,
        'averageRating': 0.0,
        'topLocation': 'No items',
      };
    }

    final totalPrice = _wishlistItems.fold<double>(
        0, (sum, hotel) => sum + (hotel.fareDetail?.totalPrice ?? 0));
    final averagePrice = totalPrice / _wishlistItems.length;

    final totalRating = _wishlistItems.fold<double>(
        0, (sum, hotel) => sum + (hotel.starRating ?? 0));
    final averageRating = totalRating / _wishlistItems.length;

    // Find most common location
    final locationCounts = <String, int>{};
    for (final hotel in _wishlistItems) {
      final location = hotel.city ?? 'Unknown';
      locationCounts[location] = (locationCounts[location] ?? 0) + 1;
    }

    final topLocation = locationCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return {
      'totalItems': _wishlistItems.length,
      'averagePrice': averagePrice,
      'averageRating': averageRating,
      'topLocation': topLocation,
    };
  }
}
