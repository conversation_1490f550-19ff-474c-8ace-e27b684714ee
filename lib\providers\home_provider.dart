import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hotel_booking/models/tourist_place_model.dart';
import 'package:hotel_booking/screens/homescreen/widgets/guest_selection_widget.dart';

class HomeProvider with ChangeNotifier {
  // Controllers
  ScrollController scrollController = ScrollController();
  final TextEditingController destinationController = TextEditingController();
  final TextEditingController guestsController =
      TextEditingController(text: '2 Adults\n1 Room');

  // Room selection data
  List<RoomData> rooms = [RoomData(adults: 2, children: 0)];

  bool _isLoading = true;
  bool get isLoading => _isLoading;

  List<TouristPlaces> _places = [];
  List<TouristPlaces> get places => _places;

  // Tourist places data

  // Date selection
  DateTime? checkInDate;
  DateTime? checkOutDate;

  // Guest selection
  int? guestCount;
  int roomCount = 1;

  // UI state
  bool isAppBarVisible = true;
  double appBarHeight = 0;


    Future<void> loadUsers() async {
    try {
      _isLoading = true;
      notifyListeners();

      final String response = await rootBundle.loadString('assets/tourist places.json')
          .catchError((error) {
    
        return '''[
          {
            "country": "United States",
            "region": "North America",
            "places": ["New York", "Los Angeles", "Chicago", "Miami", "Las Vegas"]
          },
          {
            "country": "France",
            "region": "Europe",
            "places": ["Paris", "Nice", "Lyon", "Marseille", "Bordeaux"]
          },
          {
            "country": "Japan",
            "region": "Asia",
            "places": ["Tokyo", "Kyoto", "Osaka", "Hiroshima", "Sapporo"]
          }
        ]''';
      });

      final data = json.decode(response) as List;
      _places = data.map((json) => TouristPlaces.fromJson(json)).toList();
    } catch (e) {
      // Handle any errors
      _places = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }


  // Update check-in date
  void setCheckInDate(DateTime date) {
    checkInDate = date;
    // If check-out date is before check-in date, adjust it
    if (checkOutDate != null && checkOutDate!.isBefore(checkInDate!)) {
      checkOutDate = checkInDate!.add(const Duration(days: 1));
    }
    notifyListeners();
  }

  // Update check-out date
  void setCheckOutDate(DateTime date) {
    checkOutDate = date;
    notifyListeners();
  }

  // Update guest count
  void setGuestCount(int count) {
    guestCount = count;
    notifyListeners();
  }

  // Check if search is valid
  bool isSearchValid() {
    return checkInDate != null && checkOutDate != null;
  }

  // Update destination from popular destinations
  void setDestination(String destination) {
    destinationController.text = destination;
    notifyListeners();
  }

  // Popular destinations data
  List<Map<String, String>> get popularDestinations => [
        {
          'name': 'New York',
          'image':
              'https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9'
        },
        {
          'name': 'Paris',
          'image':
              'https://images.unsplash.com/photo-1502602898657-3e91760cbb34'
        },
        {
          'name': 'Tokyo',
          'image':
              'https://images.unsplash.com/photo-1503899036084-c55cdd92da26'
        },
        {
          'name': 'Dubai',
          'image':
              'https://images.unsplash.com/photo-1512453979798-5ea266f8880c'
        },
      ];

  // Special offers data
  List<Map<String, String>> get specialOffers => [
        {
          'title': 'Luxury Resort',
          'location': 'Maldives',
          'discount': '30% Off',
          'image':
              'https://images.unsplash.com/photo-1540541338287-41700207dee6',
          'price': '\$299',
          'rating': '4.8',
        },
        {
          'title': 'Mountain Retreat',
          'location': 'Swiss Alps',
          'discount': '25% Off',
          'image':
              'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4',
          'price': '\$199',
          'rating': '4.6',
        },
      ];

  // Update room selection
  void updateRoomSelection(List<RoomData> selectedRooms) {
    // Ensure we have at least one room
    if (selectedRooms.isEmpty) {
      selectedRooms = [RoomData(adults: 2, children: 0)];
    }

    // Create deep copies to avoid reference issues
    rooms = selectedRooms.map((room) => room.clone()).toList();

    // Calculate total guests
    int totalAdults = 0;
    int totalChildren = 0;

    for (var room in rooms) {
      totalAdults += room.adults;
      totalChildren += room.children;
    }

    // Format guest information for display
    String guestInfo = '$totalAdults ${totalAdults == 1 ? 'Adult' : 'Adults'}';
    if (totalChildren > 0) {
      guestInfo +=
          ', $totalChildren ${totalChildren == 1 ? 'Child' : 'Children'}';
    }
    guestInfo += ' · ${rooms.length} ${rooms.length == 1 ? 'Room' : 'Rooms'}';

    // Update the guestsController text
    guestsController.text = guestInfo;

    notifyListeners();
  }

  // Dispose resources
  @override
  void dispose() {
    destinationController.dispose();
    guestsController.dispose();

    scrollController.dispose();
    super.dispose();
  }
}
