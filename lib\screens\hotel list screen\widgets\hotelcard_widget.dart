import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:hotel_booking/constants/app_images.dart';
import 'package:hotel_booking/constants/app_dimensions.dart';
import 'package:hotel_booking/models/hotel_details.dart';
import 'package:url_launcher/url_launcher.dart';

class HotelCard extends StatefulWidget {
  final InventoryInfoList hotel;
  final VoidCallback onTap;
  final List<String> imageUrls;

  const HotelCard({
    Key? key,
    required this.hotel,
    required this.onTap,
    this.imageUrls = const [],
  }) : super(key: key);

  @override
  State<HotelCard> createState() => _HotelCardState();
}

class _HotelCardState extends State<HotelCard> {
  int _currentIndex = 0;
  final CarouselController carouselController = CarouselController();
  bool _isFavorite = false;

  // Method to show share dialog
  void _showShareDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 8,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  spreadRadius: 5,
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title
                Text(
                  'Share Hotel',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Share ${widget.hotel.name ?? 'this hotel'} with friends',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textLight,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                
                // Share options
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // WhatsApp option
                    _buildShareOption(
                      icon: Icons.chat,
                      label: 'WhatsApp',
                      color: const Color(0xFF25D366),
                      onTap: () {
                        Navigator.of(context).pop();
                        _shareViaWhatsApp();
                      },
                    ),
                    
                    // Email option
                    _buildShareOption(
                      icon: Icons.email,
                      label: 'Email',
                      color: AppColors.primary,
                      onTap: () {
                        Navigator.of(context).pop();
                        _shareViaEmail();
                      },
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                // Cancel button
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: AppColors.textLight,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShareOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(15),
      child: Container(
        width: 80,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Method to share via WhatsApp
  void _shareViaWhatsApp() async {
    final String hotelName = widget.hotel.name ?? 'Amazing Hotel';
    final String location = "${widget.hotel.locality ?? 'Goa'}, ${widget.hotel.city ?? ''}";
    final String price = '₹${widget.hotel.fareDetail?.totalPrice ?? 0}';
    final String rating = widget.hotel.starRating?.toStringAsFixed(1) ?? '4.5';
    
    // Create a shareable link (you can replace this with your actual deep link)
    final String hotelLink = 'https://yourapp.com/hotel/${widget.hotel.hotelId ?? ''}';
    
    final String message = '''
🏨 *$hotelName*

📍 Location: $location
⭐ Rating: $rating/5
💰 Price: $price per night

Check out this amazing hotel I found!

$hotelLink

Book now for the best deals! 🎉
''';

    final String whatsappUrl = 'whatsapp://send?text=${Uri.encodeComponent(message)}';
    
    try {
      if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
        await launchUrl(Uri.parse(whatsappUrl));
      } else {
        // If WhatsApp is not installed, show a message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('WhatsApp is not installed on this device'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open WhatsApp'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Method to share via Email
  void _shareViaEmail() async {
    final String hotelName = widget.hotel.name ?? 'Amazing Hotel';
    final String location = "${widget.hotel.locality ?? 'Goa'}, ${widget.hotel.city ?? ''}";
    final String price = '₹${widget.hotel.fareDetail?.totalPrice ?? 0}';
    final String rating = widget.hotel.starRating?.toStringAsFixed(1) ?? '4.5';
    
    // Create a shareable link (you can replace this with your actual deep link)
    final String hotelLink = 'https://yourapp.com/hotel/${widget.hotel.hotelId ?? ''}';
    
    final String subject = 'Check out this amazing hotel: $hotelName';
    final String body = '''
Hi there!

I found this amazing hotel and thought you might be interested:

Hotel: $hotelName
Location: $location
Rating: $rating out of 5 stars
Price: $price per night

$hotelLink

Hope you find it useful!

Best regards
''';

    final String emailUrl = 'mailto:?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}';
    
    try {
      if (await canLaunchUrl(Uri.parse(emailUrl))) {
        await launchUrl(Uri.parse(emailUrl));
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No email app found on this device'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open email app'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
// Use hotel images from JSON if available, otherwise use placeholder images
    final List<String> imagesToDisplay = widget.hotel.imageInfoList != null &&
            widget.hotel.imageInfoList!.isNotEmpty
        ? [widget.hotel.imageInfoList!.first.url!]
        : AppImages.allImages.toList();

// Fixed dimensions for card
    final double baseCardHeight =
        460.0; // Increased by 10px to accommodate overflow
    final double cardHeight = baseCardHeight;
    final double detailsHeight = 280.0; // Increased by 10px
    final double borderRadius = AppDimensions.radiusExtraLarge;

// Get room details for the first room if available
    final RoomDetails? firstRoom =
        widget.hotel.roomDetails != null && widget.hotel.roomDetails!.isNotEmpty
            ? widget.hotel.roomDetails![0]
            : null;

// Feature list items based on hotel and room data
    final List<FeatureItem> features = [
      FeatureItem(Icons.apartment_outlined, 'Private suite'),
      FeatureItem(Icons.square_foot_outlined, '41m²'),
      FeatureItem(Icons.people_outline, '${firstRoom?.bed ?? 4} guests'),
      FeatureItem(Icons.king_bed_outlined, '2 beds'),
      FeatureItem(Icons.bed_outlined, '2 bedrooms'),
      FeatureItem(Icons.bathtub_outlined, '1 bathroom'),
    ];

// Split features into two rows
    final int halfLength = (features.length / 2).ceil();
    final List<FeatureItem> firstRowFeatures = features.sublist(0, halfLength);
    final List<FeatureItem> secondRowFeatures = features.sublist(halfLength);

    return InkWell(
      onTap: widget.onTap,
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingSizeSmall),
        child: Container(
          decoration: BoxDecoration(
              border: Border.all(color: AppColors.neutralLight),
              borderRadius: BorderRadius.circular(22)),
          height: cardHeight,
          child: Stack(
            children: [
              Container(
                height: cardHeight,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(borderRadius),
                  border: Border.all(
                    color: AppColors.divider,
                    width: 1.0,
                  ),
                ),
              ),

              SizedBox(
                height: 250,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(borderRadius),
                  child: CarouselSlider.builder(
                    itemCount: imagesToDisplay.length,
                    options: CarouselOptions(
                      height: cardHeight,
                      viewportFraction: 1.0,
                      autoPlayInterval: const Duration(seconds: 6),
                      autoPlayAnimationDuration: const Duration(seconds: 5),
                      autoPlayCurve: Curves.fastOutSlowIn,
                      enlargeCenterPage: false,
                      onPageChanged: (index, reason) {
                        setState(() {
                          _currentIndex = index;
                        });
                      },
                    ),
                    itemBuilder:
                        (BuildContext context, int index, int realIndex) {
                      return Image.network(
                        imagesToDisplay[index], // ✅ Uses the URL from JSON
                        width: double.infinity,
                        fit: BoxFit.cover,
                        height: cardHeight,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[200],
                            child: Center(
                              child: Icon(
                                Icons.image_not_supported_outlined,
                                color: Colors.grey[400],
                                size: 40,
                              ),
                            ),
                          );
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ),

              // Favorite button
              Positioned(
                top: AppDimensions.paddingSizeSmall,
                right: AppDimensions.paddingSizeSmall,
                child: Row(
                  children: [
                    InkWell(
                      onTap: _showShareDialog, // Updated to show share dialog
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.error.withAlpha(128),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(30),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        padding: EdgeInsets.all(AppDimensions.paddingSizeSmall),
                        child: Icon(
                          Icons.share,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                    SizedBox(width: 20),
                    InkWell(
                      onTap: () {
                        setState(() {
                          _isFavorite = !_isFavorite;
                        });
                        // Show a snackbar to indicate the action
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(_isFavorite
                                ? 'Added to wishlist'
                                : 'Removed from wishlist'),
                            duration: const Duration(seconds: 1),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(20),
                      child: Container(
                        decoration: BoxDecoration(
                          color: _isFavorite
                              ? AppColors.error
                              : AppColors.error.withAlpha(128),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(30),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        padding: EdgeInsets.all(AppDimensions.paddingSizeSmall),
                        child: Icon(
                          _isFavorite ? Icons.favorite : Icons.favorite_border,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Image indicators
              Positioned(
                top: AppDimensions.paddingSizeSmall,
                left: AppDimensions.paddingSizeSmall,
                child: Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingSizeSmall,
                      vertical: AppDimensions.paddingSizeExtraSmall),
                  decoration: BoxDecoration(
                    color: AppColors.text.withAlpha(80),
                    borderRadius:
                        BorderRadius.circular(AppDimensions.radiusSmall * 2),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: imagesToDisplay.asMap().entries.map((entry) {
                      bool isActive = _currentIndex == entry.key;
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _currentIndex = entry.key;
                          });
                        },
                        child: Container(
                          margin: EdgeInsets.symmetric(
                              horizontal:
                                  AppDimensions.paddingSizeExtraSmall - 2),
                          width: isActive ? 10 : 6,
                          height: isActive ? 10 : 6,
                          decoration: BoxDecoration(
                            color: isActive ? Colors.white : Colors.white54,
                            shape: BoxShape.circle,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),

              // Details container
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: detailsHeight,
                  decoration: BoxDecoration(
                    color: AppColors.neutralLight,
                    borderRadius: BorderRadius.only(
                      topRight:
                          Radius.circular(AppDimensions.paddingSizeOverLarge),
                      bottomLeft: Radius.circular(borderRadius),
                      bottomRight: Radius.circular(borderRadius),
                    ),
                    border: Border.all(color: AppColors.divider),
                  ),
                  padding: EdgeInsets.fromLTRB(
                      AppDimensions.paddingSizeDefault,
                      AppDimensions.paddingSizeSmall,
                      AppDimensions.paddingSizeDefault,
                      AppDimensions.paddingSizeSmall // Reduced bottom padding
                      ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Hotel name
                      Text(
                        widget.hotel.name ?? 'Blue Lagoon Resort',
                        style: const TextStyle(
                          fontSize: AppDimensions.fontSizeExtraLarge,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: AppDimensions.paddingSizeExtraSmall),

                      // Star rating
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: List.generate(
                            (widget.hotel.starRating ?? 5).toInt(), (index) {
                          return const Padding(
                            padding: EdgeInsets.symmetric(vertical: 1.0),
                            child: Icon(
                              Icons.star,
                              color: AppColors.secondary,
                              size: 14,
                            ),
                          );
                        }),
                      ),
                      SizedBox(height: AppDimensions.paddingSizeExtraSmall),

                      // Location info
                      Row(
                        children: [
                          const Icon(Icons.location_on,
                              color: AppColors.primary, size: 14),
                          SizedBox(width: AppDimensions.paddingSizeExtraSmall),
                          Expanded(
                            child: Text(
                              "${widget.hotel.locality ?? 'Goa'}, ${widget.hotel.city ?? ''}",
                              style: const TextStyle(
                                color: AppColors.primary,
                                fontWeight: FontWeight.w500,
                                fontSize: AppDimensions.fontSizeExtraSmall,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: AppDimensions.paddingSizeExtraSmall),

                      // Resort badge
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: AppDimensions.paddingSizeSmall,
                            vertical: AppDimensions.paddingSizeExtraSmall),
                        decoration: BoxDecoration(
                          color: AppColors.secondary.withAlpha(40),
                          borderRadius: BorderRadius.circular(
                              AppDimensions.radiusSmall + 1),
                          border: Border.all(
                              color: AppColors.secondary.withAlpha(60)),
                        ),
                        child: const Text(
                          'Resort',
                          style: TextStyle(
                            fontSize: AppDimensions.fontSizeDefault,
                            color: AppColors.secondary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      SizedBox(height: 6), // Reduced spacing

                      // Horizontally scrollable features - TWO ROWS
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // First row of features
                          SizedBox(
                            height: 32, // Reduced height
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: firstRowFeatures.length,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: EdgeInsets.only(
                                      right: AppDimensions.paddingSizeSmall),
                                  child: _buildFeatureChip(
                                    firstRowFeatures[index].icon,
                                    firstRowFeatures[index].text,
                                  ),
                                );
                              },
                            ),
                          ),

                          SizedBox(height: 4), // Reduced spacing

                          // Second row of features
                          SizedBox(
                            height: 32, // Reduced height
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: secondRowFeatures.length,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: EdgeInsets.only(
                                      right: AppDimensions.paddingSizeSmall),
                                  child: _buildFeatureChip(
                                    secondRowFeatures[index].icon,
                                    secondRowFeatures[index].text,
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 8), // Reduced spacing

                      // Price and remaining info
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Price information
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    '₹${widget.hotel.fareDetail!.displayedBaseFare ?? 0}',
                                    style: TextStyle(
                                      fontSize: AppDimensions.fontSizeDefault,
                                      color: AppColors.textLight,
                                      decoration: TextDecoration.lineThrough,
                                    ),
                                  ),
                                  SizedBox(
                                      width:
                                          AppDimensions.paddingSizeExtraSmall +
                                              1),
                                  Text(
                                    '₹${widget.hotel.fareDetail!.totalPrice ?? 0} night',
                                    style: const TextStyle(
                                      fontSize: AppDimensions.fontSizeDefault,
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                              Text(
                                '₹${(widget.hotel.fareDetail!.totalPrice ?? 0) * 3} total',
                                style: TextStyle(
                                  fontSize:
                                      AppDimensions.fontSizeExtraLarge + 2,
                                  color: AppColors.primary,
                                ),
                              ),
                            ],
                          ),

                          // Tax info tag
                          Container(
                            padding: EdgeInsets.symmetric(
                                vertical: AppDimensions.paddingSizeExtraSmall,
                                horizontal: AppDimensions.paddingSizeSmall),
                            decoration: BoxDecoration(
                              color: AppColors.error.withAlpha(20),
                              borderRadius: BorderRadius.circular(
                                  AppDimensions.radiusSmall - 1),
                              border: Border.all(
                                  color: AppColors.error.withAlpha(40)),
                            ),
                            child: Text(
                              'GST: ${widget.hotel.taxesAndCharges ?? 18}%',
                              style: const TextStyle(
                                color: AppColors.error,
                                fontWeight: FontWeight.w500,
                                fontSize: AppDimensions.fontSizeSmall,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Rating widget
              Positioned(
                right: 0,
                top: cardHeight - detailsHeight,
                child: Container(
                  width: 120,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topRight:
                          Radius.circular(AppDimensions.paddingSizeOverLarge),
                      bottomLeft:
                          Radius.circular(AppDimensions.paddingSizeOverLarge),
                    ),
                    border: Border.all(
                      color: AppColors.divider,
                      width: 1.0,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.neutralDark.withAlpha(25),
                        blurRadius: 10,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingSizeSmall,
                      vertical: AppDimensions.paddingSizeSmall),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(width: 5, height: 40, child: VerticalDivider()),
                      SizedBox(width: AppDimensions.paddingSizeSmall),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Text(
                                widget.hotel.starRating?.toStringAsFixed(1) ??
                                    '4.5',
                                style: const TextStyle(
                                  color: AppColors.secondary,
                                  fontSize: AppDimensions.fontSizeExtraLarge,
                                  fontWeight: FontWeight.bold,
                                  height: 1.0,
                                ),
                              ),
                              Text(
                                ' (${widget.hotel.userRatingCount ?? 0})',
                                style: TextStyle(
                                  color: AppColors.textLight,
                                  fontSize: AppDimensions.fontSizeExtraSmall,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: AppDimensions.paddingSizeExtraSmall),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: AppDimensions.paddingSizeSmall,
                                vertical: AppDimensions.paddingSizeExtraSmall),
                            decoration: BoxDecoration(
                              color: AppColors.secondary.withAlpha(30),
                              borderRadius: BorderRadius.circular(
                                  AppDimensions.radiusSmall - 1),
                              border: Border.all(
                                  color: AppColors.secondary.withAlpha(50)),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Reviews ',
                                  style: TextStyle(
                                    color: AppColors.textLight,
                                    fontSize: AppDimensions.fontSizeExtraSmall,
                                  ),
                                ),
                                Text(
                                  '${widget.hotel.userRating ?? 0}',
                                  style: const TextStyle(
                                    color: AppColors.secondary,
                                    fontWeight: FontWeight.bold,
                                    fontSize: AppDimensions.fontSizeSmall,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureChip(IconData icon, String text) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingSizeSmall,
          vertical: 4 // Fixed smaller padding
          ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall * 2),
        border: Border.all(
            color: AppColors.divider
                .withAlpha(100)), // Added border for better definition
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon,
              size: 14,
              color: AppColors.primary.withAlpha(180)), // Smaller icon
          SizedBox(width: 4), // Smaller spacing
          Text(
            text,
            style: TextStyle(
              fontSize: AppDimensions.fontSizeSmall,
              color: AppColors.primary.withAlpha(180),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

// Feature item model
class FeatureItem {
  final IconData icon;
  final String text;

  FeatureItem(this.icon, this.text);
}