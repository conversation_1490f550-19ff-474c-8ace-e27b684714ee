import 'package:flutter/material.dart';

import 'dart:convert';

import 'package:hotel_booking/models/hotel_details.dart';


class FavoritesProvider extends ChangeNotifier {
  List<InventoryInfoList> _favorites = [];
  bool _isLoading = true;
  String? _error;

  List<InventoryInfoList> get favorites => _favorites;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Check if a hotel is in favorites
  bool isFavorite(String hotelId) {
    return _favorites.any((hotel) => hotel.hotelId.toString() == hotelId);
  }

  // Get count of favorites
  int get favoritesCount => _favorites.length;

  // Load favorites from local storage or backend API
  Future<void> loadFavorites() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Simulate API call with a delay
      await Future.delayed(const Duration(seconds: 1));

      // Mock data - in real app, fetch from API or local storage
      // _favorites = [
      //   Hotel(
      //     id: '1',
      //     name: 'Grand Plaza Hotel',
      //     location: 'New York',
      //     rating: 4.8,
      //     price: 199,
      //     imageUrl: 'https://example.com/grand_plaza.jpg',
      //     description: 'Luxury hotel in the heart of New York with stunning views.',
      //     amenities: ['Free WiFi', 'Pool', 'Spa', 'Gym', 'Restaurant'],
      //     distanceFromCenter: 0.8,
      //     reviewCount: 247,
      //     hasPromotion: true,
      //   ),
      //   Hotel(
      //     id: '2',
      //     name: 'Seaside Resort',
      //     location: 'Miami',
      //     rating: 4.5,
      //     price: 149,
      //     imageUrl: 'https://example.com/seaside_resort.jpg',
      //     description: 'Beachfront resort with private access to Miami Beach.',
      //     amenities: ['Free WiFi', 'Pool', 'Beach Access', 'Bar', 'Parking'],
      //     distanceFromCenter: 2.5,
      //     reviewCount: 189,
      //     hasPromotion: false,
      //   ),
      //   Hotel(
      //     id: '3',
      //     name: 'Mountain View Lodge',
      //     location: 'Denver',
      //     rating: 4.7,
      //     price: 179,
      //     imageUrl: 'https://example.com/mountain_lodge.jpg',
      //     description: 'Cozy lodge with breathtaking mountain views.',
      //     amenities: ['Free WiFi', 'Fireplace', 'Breakfast', 'Hiking Trails', 'Parking'],
      //     distanceFromCenter: 5.2,
      //     reviewCount: 156,
      //     hasPromotion: true,
      //   ),
      // ];

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load favorites: ${e.toString()}';
      notifyListeners();
    }
  }

  // Remove hotel from favorites
  void removeFromFavorites(InventoryInfoList hotel) {
    _favorites.removeWhere((item) => item.hotelId == hotel.hotelId);
    notifyListeners();

    // In real app, you'd also update this in backend/storage
    // saveToStorage() or API call
  }

  // Add hotel to favorites
  void addToFavorites(InventoryInfoList hotel) {
    if (!_favorites.any((item) => item.hotelId == hotel.hotelId)) {
      _favorites.add(hotel);
      notifyListeners();

      
    }
  }

  // Navigate to hotel details
  void navigateToHotelDetails(BuildContext context, InventoryInfoList hotel) {
    // Implement navigation to hotel detailss
    // Example:
    // Navigator.push(
    //   context,
    //   MaterialPageRoute(
    //     builder: (context) => HotelDetailsScreen(hotel: hotel),
    //   ),
    // );

    // For now, just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing details for ${hotel.name}')),
    );
  }
}

