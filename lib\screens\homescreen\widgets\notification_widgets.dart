import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_text_styles.dart';

class NotificationWidgets extends StatelessWidget {
  const NotificationWidgets({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        title: Text(
          'Notifications',
          style: AppTextStyles.headline3.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _buildNotificationList(),
    );
  }

  Widget _buildNotificationList() {
    // Sample notification data
    final List<NotificationItem> notifications = [
      NotificationItem(
        title: 'Booking Confirmed',
        message: 'Your booking at Grand Plaza Hotel has been confirmed for June 15-18.',
        time: '2 hours ago',
        isRead: false,
        icon: Icons.check_circle,
        iconColor: Colors.green,
      ),
      NotificationItem(
        title: 'Special Offer',
        message: 'Get 20% off on your next booking with code SUMMER20.',
        time: '1 day ago',
        isRead: false,
        icon: Icons.local_offer,
        iconColor: AppColors.accent,
      ),
      NotificationItem(
        title: 'Payment Successful',
        message: 'Your payment of \$350 for Seaside Resort has been processed successfully.',
        time: '2 days ago',
        isRead: true,
        icon: Icons.payment,
        iconColor: AppColors.primary,
      ),
      NotificationItem(
        title: 'Upcoming Stay',
        message: 'Your stay at Mountain View Lodge is in 3 days. We\'re excited to welcome you!',
        time: '3 days ago',
        isRead: true,
        icon: Icons.hotel,
        iconColor: AppColors.secondary,
      ),
      NotificationItem(
        title: 'Rate Your Experience',
        message: 'How was your stay at Sunset Beach Resort? Tap to rate your experience.',
        time: '1 week ago',
        isRead: true,
        icon: Icons.star,
        iconColor: Colors.amber,
      ),
      NotificationItem(
        title: 'Account Update',
        message: 'Your profile information has been updated successfully.',
        time: '2 weeks ago',
        isRead: true,
        icon: Icons.person,
        iconColor: Colors.blue,
      ),
    ];

    if (notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_off,
              size: 80,
              color: AppColors.textLight.withAlpha(120),
            ),
            const SizedBox(height: 16),
            Text(
              'No notifications yet',
              style: AppTextStyles.subtitle1.copyWith(
                color: AppColors.textLight,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'We\'ll notify you when something arrives',
              style: AppTextStyles.bodyText2,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          color: AppColors.primary.withAlpha(20),
          child: Row(
            children: [
              Text(
                'You have ${notifications.where((n) => !n.isRead).length} unread notifications',
                style: AppTextStyles.bodyText2.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  // Mark all as read functionality would go here
                },
                child: Text(
                  'Mark all as read',
                  style: AppTextStyles.bodyText2.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              final notification = notifications[index];
              return NotificationCard(notification: notification);
            },
          ),
        ),
      ],
    );
  }
}

class NotificationItem {
  final String title;
  final String message;
  final String time;
  final bool isRead;
  final IconData icon;
  final Color iconColor;

  NotificationItem({
    required this.title,
    required this.message,
    required this.time,
    required this.isRead,
    required this.icon,
    required this.iconColor,
  });
}

class NotificationCard extends StatelessWidget {
  final NotificationItem notification;

  const NotificationCard({
    Key? key,
    required this.notification,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: notification.isRead ? Colors.white : AppColors.primary.withAlpha(10),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.divider.withAlpha(100),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: notification.iconColor.withAlpha(30),
                shape: BoxShape.circle,
              ),
              child: Icon(
                notification.icon,
                color: notification.iconColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          notification.title,
                          style: AppTextStyles.subtitle2.copyWith(
                            fontWeight: notification.isRead ? FontWeight.w600 : FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (!notification.isRead)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    notification.message,
                    style: AppTextStyles.bodyText2.copyWith(
                      color: AppColors.text.withAlpha(220),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        notification.time,
                        style: AppTextStyles.caption.copyWith(
                          color: AppColors.textLight,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          // Action for more options
                        },
                        child: Icon(
                          Icons.more_horiz,
                          color: AppColors.textLight,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}