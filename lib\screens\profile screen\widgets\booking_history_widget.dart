import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_text_styles.dart';
import 'package:hotel_booking/screens/itinerary%20screen/itinerary_screen.dart';

class BookingHistoryWidget extends StatefulWidget {
  const BookingHistoryWidget({super.key});

  @override
  State<BookingHistoryWidget> createState() => _BookingHistoryWidgetState();
}

class _BookingHistoryWidgetState extends State<BookingHistoryWidget> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Sample booking data - in a real app, this would come from an API or database
  final List<Map<String, dynamic>> _upcomingBookings = [
    {
      'id': 'BK78945612',
      'hotelName': 'Luxury Resort & Spa',
      'location': 'Dubai, UAE',
      'roomType': 'Deluxe Room',
      'checkInDate': 'May 15, 2023',
      'checkOutDate': 'May 18, 2023',
      'numberOfGuests': 2,
      'totalAmount': 400.0,
      'status': 'Confirmed',
      'imageUrl': 'assets/images/sara-dubler-<PERSON>ei_7yYtIo-unsplash.jpg',
    },
    {
      'id': 'BK78945613',
      'hotelName': 'Grand Plaza Hotel',
      'location': 'New York, USA',
      'roomType': 'Executive Suite',
      'checkInDate': 'Jun 10, 2023',
      'checkOutDate': 'Jun 15, 2023',
      'numberOfGuests': 2,
      'totalAmount': 750.0,
      'status': 'Pending',
      'imageUrl': 'assets/images/sara-dubler-Koei_7yYtIo-unsplash.jpg',
    },
  ];

  final List<Map<String, dynamic>> _pastBookings = [
    {
      'id': 'BK78945610',
      'hotelName': 'Seaside Resort',
      'location': 'Bali, Indonesia',
      'roomType': 'Ocean View Room',
      'checkInDate': 'Jan 5, 2023',
      'checkOutDate': 'Jan 10, 2023',
      'numberOfGuests': 2,
      'totalAmount': 550.0,
      'status': 'Completed',
      'imageUrl': 'assets/images/sara-dubler-Koei_7yYtIo-unsplash.jpg',
      'rating': 4.5,
    },
    {
      'id': 'BK78945609',
      'hotelName': 'Mountain Retreat',
      'location': 'Swiss Alps, Switzerland',
      'roomType': 'Chalet Suite',
      'checkInDate': 'Dec 20, 2022',
      'checkOutDate': 'Dec 27, 2022',
      'numberOfGuests': 4,
      'totalAmount': 1200.0,
      'status': 'Completed',
      'imageUrl': 'assets/images/sara-dubler-Koei_7yYtIo-unsplash.jpg',
      'rating': 5.0,
    },
    {
      'id': 'BK78945608',
      'hotelName': 'City Center Hotel',
      'location': 'Paris, France',
      'roomType': 'Standard Room',
      'checkInDate': 'Oct 10, 2022',
      'checkOutDate': 'Oct 15, 2022',
      'numberOfGuests': 2,
      'totalAmount': 480.0,
      'status': 'Completed',
      'imageUrl': 'assets/images/sara-dubler-Koei_7yYtIo-unsplash.jpg',
      'rating': 4.0,
    },
  ];

  final List<Map<String, dynamic>> _cancelledBookings = [
    {
      'id': 'BK78945607',
      'hotelName': 'Beach Resort',
      'location': 'Cancun, Mexico',
      'roomType': 'Beach Front Villa',
      'checkInDate': 'Mar 15, 2023',
      'checkOutDate': 'Mar 20, 2023',
      'numberOfGuests': 2,
      'totalAmount': 850.0,
      'status': 'Cancelled',
      'imageUrl': 'assets/images/sara-dubler-Koei_7yYtIo-unsplash.jpg',
      'cancellationReason': 'Personal emergency',
      'refundAmount': 765.0,
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking History'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withAlpha(180),
          tabs: const [
            Tab(text: 'Upcoming'),
            Tab(text: 'Past'),
            Tab(text: 'Cancelled'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Upcoming bookings tab
          _buildBookingsList(_upcomingBookings, 'upcoming'),

          // Past bookings tab
          _buildBookingsList(_pastBookings, 'past'),

          // Cancelled bookings tab
          _buildBookingsList(_cancelledBookings, 'cancelled'),
        ],
      ),
    );
  }

  Widget _buildBookingsList(List<Map<String, dynamic>> bookings, String type) {
    if (bookings.isEmpty) {
      return _buildEmptyState(type);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      itemBuilder: (context, index) {
        final booking = bookings[index];
        return _buildBookingCard(booking, type);
      },
    );
  }

  Widget _buildBookingCard(Map<String, dynamic> booking, String type) {
    Color statusColor;
    IconData statusIcon;

    switch (booking['status']) {
      case 'Confirmed':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'Pending':
        statusColor = Colors.orange;
        statusIcon = Icons.access_time;
        break;
      case 'Completed':
        statusColor = Colors.blue;
        statusIcon = Icons.done_all;
        break;
      case 'Cancelled':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.info;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // Navigate to booking details
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ItineraryScreen(
                bookingId: booking['id'],
                hotelName: booking['hotelName'],
                roomType: booking['roomType'],
                checkInDate: booking['checkInDate'],
                checkOutDate: booking['checkOutDate'],
                numberOfGuests: booking['numberOfGuests'],
                totalAmount: booking['totalAmount'],
                guestName: 'John Doe',
                guestEmail: '<EMAIL>',
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            // Hotel image
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              child: Image.asset(
                booking['imageUrl'],
                height: 150,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  height: 150,
                  width: double.infinity,
                  color: Colors.grey.shade300,
                  child: const Icon(
                    Icons.image,
                    size: 50,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),

            // Booking details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Hotel name and status
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          booking['hotelName'],
                          style: AppTextStyles.headline3.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: statusColor.withAlpha(30),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              statusIcon,
                              color: statusColor,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              booking['status'],
                              style: TextStyle(
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Location
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        booking['location'],
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Booking details
                  Row(
                    children: [
                      _buildDetailItem(
                        Icons.calendar_today,
                        'Check-in',
                        booking['checkInDate'],
                      ),
                      _buildDetailItem(
                        Icons.calendar_today,
                        'Check-out',
                        booking['checkOutDate'],
                      ),
                      _buildDetailItem(
                        Icons.person,
                        'Guests',
                        '${booking['numberOfGuests']} Guests',
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Room type and price
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.hotel,
                            size: 16,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            booking['roomType'],
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '\$${booking['totalAmount'].toStringAsFixed(2)}',
                        style: TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),

                  // Show rating for past bookings
                  if (type == 'past' && booking.containsKey('rating')) ...[
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Text(
                          'Your Rating: ',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 4),
                        ...List.generate(5, (index) {
                          return Icon(
                            index < booking['rating'].floor() ? Icons.star :
                            (index < booking['rating'] ? Icons.star_half : Icons.star_border),
                            color: Colors.amber,
                            size: 18,
                          );
                        }),
                        const SizedBox(width: 4),
                        Text(
                          booking['rating'].toString(),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],

                  // Show cancellation details for cancelled bookings
                  if (type == 'cancelled' && booking.containsKey('cancellationReason')) ...[
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 8),
                    Text(
                      'Cancellation Reason: ${booking['cancellationReason']}',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Refund Amount: \$${booking['refundAmount'].toStringAsFixed(2)}',
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                  ],

                  // Action buttons
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      if (type == 'upcoming') ...[
                        _buildActionButton(
                          'Modify',
                          Icons.edit,
                          Colors.blue,
                          () {
                            // Handle modify booking
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Modify booking...')),
                            );
                          },
                        ),
                        const SizedBox(width: 8),
                        _buildActionButton(
                          'Cancel',
                          Icons.cancel,
                          Colors.red,
                          () {
                            // Handle cancel booking
                            _showCancelDialog(booking);
                          },
                        ),
                      ],
                      if (type == 'past') ...[
                        _buildActionButton(
                          'Book Again',
                          Icons.refresh,
                          Colors.green,
                          () {
                            // Handle book again
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Booking again...')),
                            );
                          },
                        ),
                        const SizedBox(width: 8),
                        _buildActionButton(
                          'Review',
                          Icons.rate_review,
                          Colors.amber,
                          () {
                            // Handle review
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Write a review...')),
                            );
                          },
                        ),
                      ],
                      if (type == 'cancelled') ...[
                        _buildActionButton(
                          'Book Again',
                          Icons.refresh,
                          Colors.green,
                          () {
                            // Handle book again
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Booking again...')),
                            );
                          },
                        ),
                      ],
                      const Spacer(),
                      _buildActionButton(
                        'Details',
                        Icons.arrow_forward,
                        AppColors.primary,
                        () {
                          // Navigate to booking details
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ItineraryScreen(
                                bookingId: booking['id'],
                                hotelName: booking['hotelName'],
                                roomType: booking['roomType'],
                                checkInDate: booking['checkInDate'],
                                checkOutDate: booking['checkOutDate'],
                                numberOfGuests: booking['numberOfGuests'],
                                totalAmount: booking['totalAmount'],
                                guestName: 'John Doe',
                                guestEmail: '<EMAIL>',
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(IconData icon, String title, String value) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 14,
                color: Colors.grey,
              ),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 13,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              text,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String type) {
    String message;
    IconData icon;

    switch (type) {
      case 'upcoming':
        message = 'You have no upcoming bookings';
        icon = Icons.event_available;
        break;
      case 'past':
        message = 'You have no past bookings';
        icon = Icons.history;
        break;
      case 'cancelled':
        message = 'You have no cancelled bookings';
        icon = Icons.cancel;
        break;
      default:
        message = 'No bookings found';
        icon = Icons.hotel;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 24),
          if (type == 'upcoming')
            ElevatedButton(
              onPressed: () {
                // Navigate to hotel search
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Book a Hotel'),
            ),
        ],
      ),
    );
  }

  void _showCancelDialog(Map<String, dynamic> booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to cancel your booking at ${booking['hotelName']}?'),
            const SizedBox(height: 16),
            const Text(
              'Cancellation Policy:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '• Free cancellation until 48 hours before check-in\n'
              '• If you cancel within 48 hours of check-in, the first night will be charged',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('No, Keep Booking'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Handle cancel booking
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Booking cancelled successfully')),
              );

              // In a real app, you would call an API to cancel the booking
              // and then update the UI accordingly
              setState(() {
                _upcomingBookings.removeWhere((b) => b['id'] == booking['id']);
                _cancelledBookings.add({
                  ...booking,
                  'status': 'Cancelled',
                  'cancellationReason': 'User cancelled',
                  'refundAmount': booking['totalAmount'] * 0.9, // 90% refund
                });
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Yes, Cancel Booking'),
          ),
        ],
      ),
    );
  }
}