
// import 'package:flutter/material.dart';
// import 'package:hotel_booking/constants/app_colors.dart';
// import 'package:hotel_booking/constants/app_images.dart';
// import 'package:hotel_booking/constants/app_text_styles.dart';
// import 'package:hotel_booking/providers/auth_provider.dart';
// import 'package:hotel_booking/routes/app_routes.dart';
// import 'package:hotel_booking/widgets/custombutton_widget.dart';
// import 'package:provider/provider.dart';

// class SignupScreen extends StatefulWidget {
//   const SignupScreen({super.key});

//   @override
//   State<SignupScreen> createState() => _SignupScreenState();
// }

// class _SignupScreenState extends State<SignupScreen> {
//   final _formKey = GlobalKey<FormState>();
//   final _nameController = TextEditingController();
//   final _emailController = TextEditingController();
//   final _passwordController = TextEditingController();
//   final _confirmPasswordController = TextEditingController();
//   bool _obscurePassword = true;
//   bool _obscureConfirmPassword = true;
//   bool _agreeToTerms = true;

//   @override
//   void dispose() {
//     _nameController.dispose();
//     _emailController.dispose();
//     _passwordController.dispose();
//     _confirmPasswordController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final screenHeight = MediaQuery.of(context).size.height;

//     return Scaffold(
//       backgroundColor: AppColors.background,
//       body: Column(
//         children: [
//           // Top image section (30% of screen)
//           Container(
//             height: screenHeight * 0.3,
//             width: double.infinity,
//             decoration: BoxDecoration(
//               image: DecorationImage(
//                 image: AssetImage(AppImages.background),
//                 fit: BoxFit.cover,
//                 colorFilter: ColorFilter.mode(
//                   Colors.black.withAlpha(128),
//                   BlendMode.darken,
//                 ),
//               ),
//             ),
//             child: SafeArea(
//               child: Padding(
//                 padding: const EdgeInsets.symmetric(horizontal: 20.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     const SizedBox(height: 20),
//                     // Back button and app name
//                     Row(
//                       children: [
//                         IconButton(
//                           icon: const Icon(
//                             Icons.arrow_back_ios,
//                             color: Colors.white,
//                           ),
//                           onPressed: () => Navigator.pop(context),
//                         ),
//                         Container(
//                           padding: const EdgeInsets.all(8),
//                           decoration: BoxDecoration(
//                             color: Colors.white.withAlpha(50),
//                             borderRadius: BorderRadius.circular(12),
//                           ),
//                           child: Image.asset(
//                             AppImages.logo,
//                             height: 32,
//                             width: 32,
//                             color: Colors.white,
//                           ),
//                         ),
//                         const SizedBox(width: 12),
//                         Text(
//                           'KindAli Travel',
//                           style: AppTextStyles.headline2.copyWith(
//                             color: Colors.white,
//                             fontSize: 20,
//                           ),
//                         ),
//                       ],
//                     ),

//                     const Spacer(),

//                     // Create account text
//                     Text(
//                       'Create Account',
//                       style: AppTextStyles.headline1.copyWith(
//                         color: Colors.white,
//                         fontSize: 28,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     const SizedBox(height: 8),
//                     Container(
//                       width: 40,
//                       height: 3,
//                       decoration: BoxDecoration(
//                         color: AppColors.accent,
//                         borderRadius: BorderRadius.circular(1.5),
//                       ),
//                     ),
//                     const SizedBox(height: 8),
//                     Text(
//                       'Sign up to get started',
//                       style: TextStyle(
//                         color: Colors.white.withAlpha(230),
//                         fontSize: 16,
//                       ),
//                     ),
//                     const SizedBox(height: 20),
//                   ],
//                 ),
//               ),
//             ),
//           ),

//           // Bottom container (70% of screen with rounded top corners)
//           Expanded(
//             child: Container(
//               width: double.infinity,
//               decoration: const BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: BorderRadius.only(
//                   topLeft: Radius.circular(30),
//                   topRight: Radius.circular(30),
//                 ),
//                 boxShadow: [
//                   BoxShadow(
//                     color: Color(0x1A000000),
//                     blurRadius: 10,
//                     offset: Offset(0, -4),
//                   ),
//                 ],
//               ),
//               child: SingleChildScrollView(
//                 physics: const BouncingScrollPhysics(),
//                 child: Padding(
//                   padding: const EdgeInsets.fromLTRB(24, 30, 24, 24),
//                   child: Form(
//                     key: _formKey,
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.stretch,
//                       children: [
//                         // Name field with border
//                         Container(
//                           decoration: BoxDecoration(
//                             border: Border.all(color: AppColors.divider),
//                             borderRadius: const BorderRadius.only(
//                               topLeft: Radius.circular(12),
//                               topRight: Radius.circular(12),
//                             ),
//                           ),
//                           child: TextFormField(
//                             controller: _nameController,
//                             decoration: InputDecoration(
//                               labelText: 'Full Name',
//                               labelStyle: TextStyle(color: AppColors.textLight),
//                               hintText: 'Enter your full name',
//                               prefixIcon: Icon(Icons.person_outline, color: AppColors.primary),
//                               border: InputBorder.none,
//                               contentPadding: const EdgeInsets.symmetric(
//                                 vertical: 16,
//                                 horizontal: 20,
//                               ),
//                             ),
//                             validator: (value) {
//                               if (value == null || value.isEmpty) {
//                                 return 'Please enter your name';
//                               }
//                               return null;
//                             },
//                           ),
//                         ),
//                         const SizedBox(height: 20),

//                         // Email field with border
//                         Container(
//                           decoration: BoxDecoration(
//                             border: Border.all(color: AppColors.divider),
//                             borderRadius: const BorderRadius.only(
//                               topLeft: Radius.circular(12),
//                               topRight: Radius.circular(12),
//                             ),
//                           ),
//                           child: TextFormField(
//                             controller: _emailController,
//                             keyboardType: TextInputType.emailAddress,
//                             decoration: InputDecoration(
//                               labelText: 'Email',
//                               labelStyle: TextStyle(color: AppColors.textLight),
//                               hintText: 'Enter your email',
//                               prefixIcon: Icon(Icons.email_outlined, color: AppColors.primary),
//                               border: InputBorder.none,
//                               contentPadding: const EdgeInsets.symmetric(
//                                 vertical: 16,
//                                 horizontal: 20,
//                               ),
//                             ),
//                             validator: (value) {
//                               if (value == null || value.isEmpty) {
//                                 return 'Please enter your email';
//                               } else if (!value.contains('@') || !value.contains('.')) {
//                                 return 'Please enter a valid email';
//                               }
//                               return null;
//                             },
//                           ),
//                         ),
//                         const SizedBox(height: 20),

//                         // Password field with border
//                         Container(
//                           decoration: BoxDecoration(
//                             border: Border.all(color: AppColors.divider),
//                             borderRadius: const BorderRadius.only(
//                               topLeft: Radius.circular(12),
//                               topRight: Radius.circular(12),
//                             ),
//                           ),
//                           child: TextFormField(
//                             controller: _passwordController,
//                             obscureText: _obscurePassword,
//                             decoration: InputDecoration(
//                               labelText: 'Password',
//                               labelStyle: TextStyle(color: AppColors.textLight),
//                               hintText: 'Enter your password',
//                               prefixIcon: Icon(Icons.lock_outline, color: AppColors.primary),
//                               suffixIcon: IconButton(
//                                 icon: Icon(
//                                   _obscurePassword
//                                       ? Icons.visibility_outlined
//                                       : Icons.visibility_off_outlined,
//                                   color: AppColors.primary,
//                                 ),
//                                 onPressed: () {
//                                   setState(() {
//                                     _obscurePassword = !_obscurePassword;
//                                   });
//                                 },
//                               ),
//                               border: InputBorder.none,
//                               contentPadding: const EdgeInsets.symmetric(
//                                 vertical: 16,
//                                 horizontal: 20,
//                               ),
//                             ),
//                             validator: (value) {
//                               if (value == null || value.isEmpty) {
//                                 return 'Please enter a password';
//                               } else if (value.length < 6) {
//                                 return 'Password must be at least 6 characters';
//                               }
//                               return null;
//                             },
//                           ),
//                         ),
//                         const SizedBox(height: 20),

//                         // Confirm password field with border
//                         Container(
//                           decoration: BoxDecoration(
//                             border: Border.all(color: AppColors.divider),
//                             borderRadius: const BorderRadius.only(
//                               topLeft: Radius.circular(12),
//                               topRight: Radius.circular(12),
//                             ),
//                           ),
//                           child: TextFormField(
//                             controller: _confirmPasswordController,
//                             obscureText: _obscureConfirmPassword,
//                             decoration: InputDecoration(
//                               labelText: 'Confirm Password',
//                               labelStyle: TextStyle(color: AppColors.textLight),
//                               hintText: 'Confirm your password',
//                               prefixIcon: Icon(Icons.lock_outline, color: AppColors.primary),
//                               suffixIcon: IconButton(
//                                 icon: Icon(
//                                   _obscureConfirmPassword
//                                       ? Icons.visibility_outlined
//                                       : Icons.visibility_off_outlined,
//                                   color: AppColors.primary,
//                                 ),
//                                 onPressed: () {
//                                   setState(() {
//                                     _obscureConfirmPassword = !_obscureConfirmPassword;
//                                   });
//                                 },
//                               ),
//                               border: InputBorder.none,
//                               contentPadding: const EdgeInsets.symmetric(
//                                 vertical: 16,
//                                 horizontal: 20,
//                               ),
//                             ),
//                             validator: (value) {
//                               if (value == null || value.isEmpty) {
//                                 return 'Please confirm your password';
//                               } else if (value != _passwordController.text) {
//                                 return 'Passwords do not match';
//                               }
//                               return null;
//                             },
//                           ),
//                         ),
//                         const SizedBox(height: 20),

//                         // Terms and conditions
//                         Row(
//                           children: [
//                             Checkbox(
//                               value: _agreeToTerms,
//                               onChanged: (value) {
//                                 setState(() {
//                                   _agreeToTerms = value ?? true;
//                                 });
//                               },
//                               activeColor: AppColors.primary,
//                               shape: RoundedRectangleBorder(
//                                 borderRadius: BorderRadius.circular(4),
//                               ),
//                             ),
//                             Expanded(
//                               child: RichText(
//                                 text: TextSpan(
//                                   text: 'I agree to the ',
//                                   style: TextStyle(color: AppColors.textLight),
//                                   children: [
//                                     TextSpan(
//                                       text: 'Terms of Service',
//                                       style: TextStyle(
//                                         color: AppColors.primary,
//                                         fontWeight: FontWeight.bold,
//                                       ),
//                                     ),
//                                     const TextSpan(text: ' and '),
//                                     TextSpan(
//                                       text: 'Privacy Policy',
//                                       style: TextStyle(
//                                         color: AppColors.primary,
//                                         fontWeight: FontWeight.bold,
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),

//                         const SizedBox(height: 30),

//                         // Sign up button
//                         Consumer<AuthProvider>(
//                           builder: (context, authProvider, _) {
//                             return CustombuttonWidget(
//                               text: 'Sign Up',
//                               backgroundColor: AppColors.secondary,
//                               textColor: Colors.white,
//                               borderRadius: 12,
//                               height: 56,
//                               isFullWidth: true,
//                               isLoading: authProvider.isLoading,
//                               onPressed: () {
//                                 if (_formKey.currentState!.validate() && _agreeToTerms) {
//                                   authProvider.signup(
//                                     context,
//                                     _nameController.text,
//                                     _emailController.text,
//                                     _passwordController.text,
//                                   );
//                                 } else if (!_agreeToTerms) {
//                                   ScaffoldMessenger.of(context).showSnackBar(
//                                     const SnackBar(
//                                       content: Text('Please agree to the Terms of Service and Privacy Policy'),
//                                     ),
//                                   );
//                                 }
//                               },
//                               textStyle: AppTextStyles.button.copyWith(
//                                 fontSize: 18,
//                               ),
//                             );
//                           },
//                         ),

//                         const SizedBox(height: 20),

//                         // Login option
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             Text(
//                               'Already have an account?',
//                               style: TextStyle(
//                                 color: AppColors.textLight,
//                                 fontSize: 16,
//                               ),
//                             ),
//                             TextButton(
//                               onPressed: () {
//                                 Navigator.pushReplacementNamed(context, AppRoutes.login);
//                               },
//                               style: TextButton.styleFrom(
//                                 foregroundColor: AppColors.primary,
//                               ),
//                               child: Text(
//                                 'Login',
//                                 style: TextStyle(
//                                   color: AppColors.primary,
//                                   fontWeight: FontWeight.bold,
//                                   fontSize: 16,
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
