import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart'; // Add this import
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_localizations.dart';
import 'package:hotel_booking/providers/auth_provider.dart';
import 'package:hotel_booking/providers/booking_provider.dart';
import 'package:hotel_booking/providers/home_provider.dart';
import 'package:hotel_booking/providers/hotel_detailscreen_provider.dart';
import 'package:hotel_booking/providers/hotel_listscreen_provider.dart';
import 'package:hotel_booking/providers/localization_provider.dart';
import 'package:hotel_booking/providers/room_selection_provider.dart';
import 'package:hotel_booking/routes/app_routes.dart';
import 'package:hotel_booking/screens/homescreen/homescreen.dart';
import 'package:hotel_booking/screens/hotel%20list%20screen/hotel_list_screen.dart';
import 'package:hotel_booking/screens/room%20selection%20screen/booking_rooms_screen.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localization/flutter_localization.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => HomeProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => HotelListProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => HotelDetailscreenProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => AuthProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => BookingProvider(),
        ),  
        ChangeNotifierProvider(
          create: (context) => RoomSelectionProvider(),
        ), 
        ChangeNotifierProvider(
          create: (context) => LocalizationProvider(),
        ),
      ],
      child: Consumer<LocalizationProvider>(builder: (context, localizationprovider, child) {
        return MaterialApp(
          title: 'Hotel Booking App',
          debugShowCheckedModeBanner: false,
        
          // Localization setup - FIXED: Added required Material localizations
          locale: localizationprovider.locale,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,    
            GlobalWidgetsLocalizations.delegate,     
            GlobalCupertinoLocalizations.delegate,   
          ],
          supportedLocales: LocalizationProvider.supportedLocales,
        
          builder: (context, child) {
            return Directionality(
              textDirection: localizationprovider.textDirection,
              child: child!,
            );
          },
        
          theme: ThemeData(
            primarySwatch: AppColors.primaryMaterialColor,
            primaryColor: AppColors.primary,
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              secondary: AppColors.secondary,
              tertiary: AppColors.tertiary,
              surface: AppColors.surface,
              error: AppColors.error,
              onPrimary: AppColors.textOnPrimary,
              onSecondary: AppColors.textOnSecondary,
              onSurface: AppColors.text,
              onError: Colors.white,
              brightness: Brightness.light,
            ),
            scaffoldBackgroundColor: AppColors.background,
            appBarTheme: AppBarTheme(
              backgroundColor: AppColors.secondary,
              foregroundColor: Colors.white,
              elevation: 0,
            ),
            cardTheme: CardTheme(
              color: AppColors.surface,
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            buttonTheme: ButtonThemeData(
              buttonColor: AppColors.secondary,
              textTheme: ButtonTextTheme.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.secondary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 2,
                shadowColor: AppColors.shadow,
              ),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.secondary,
              ),
            ),
            outlinedButtonTheme: OutlinedButtonThemeData(
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.secondary,
                side: BorderSide(color: AppColors.secondary),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            textTheme: TextTheme(
              displayLarge: TextStyle(color: AppColors.text),
              displayMedium: TextStyle(color: AppColors.text),
              displaySmall: TextStyle(color: AppColors.text),
              headlineLarge: TextStyle(color: AppColors.text),
              headlineMedium: TextStyle(color: AppColors.text),
              headlineSmall: TextStyle(color: AppColors.text),
              titleLarge: TextStyle(color: AppColors.text),
              titleMedium: TextStyle(color: AppColors.text),
              titleSmall: TextStyle(color: AppColors.text),
              bodyLarge: TextStyle(color: AppColors.text),
              bodyMedium: TextStyle(color: AppColors.text),
              bodySmall: TextStyle(color: AppColors.textLight),
              labelLarge: TextStyle(color: AppColors.text),
              labelMedium: TextStyle(color: AppColors.text),
              labelSmall: TextStyle(color: AppColors.textLight),
            ),
            dividerTheme: DividerThemeData(
              color: AppColors.divider,
              thickness: 1,
            ),
            brightness: Brightness.light,
          ),
          darkTheme: ThemeData(
            primarySwatch: AppColors.primaryMaterialColor,
            primaryColor: AppColors.primary,
            colorScheme: ColorScheme.dark(
              primary: AppColors.primary,
              secondary: AppColors.secondary,
              tertiary: AppColors.tertiary,
              surface: Color(0xFF101036),
              error: AppColors.error,
              onPrimary: Colors.white,
              onSecondary: Colors.white,
              onSurface: Colors.white,
              onError: Colors.white,
              brightness: Brightness.dark,
            ),
            scaffoldBackgroundColor: Color(0xFF0A0A2A),
            appBarTheme: AppBarTheme(
              backgroundColor: AppColors.secondary,
              foregroundColor: Colors.white,
              elevation: 0,
            ),
            brightness: Brightness.dark,
          ),
        
          // initialRoute: AppRoutes.login,
          // routes: AppRoutes.getRoutes(),
          // onGenerateRoute: AppRoutes.generateRoute,
          home: HomeScreen(),
        );
      }),
    );
  }
}