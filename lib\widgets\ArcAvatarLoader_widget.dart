import 'dart:math';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';

class ArcavatarloaderWidget extends StatefulWidget {
  const ArcavatarloaderWidget({super.key, required this.image});
  final ImageProvider image;

  @override
  State<ArcavatarloaderWidget> createState() => _ArcavatarloaderWidgetState();
}

class _ArcavatarloaderWidgetState extends State<ArcavatarloaderWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 220,
      height: 220,
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(25),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(25),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withAlpha(50),
                width: 1.5,
              ),
            ),
            child: StaggeredDotsAvatarLoader(image: widget.image),
          ),
        ),
      ),
    );
  }
}

class StaggeredDotsAvatarLoader extends StatefulWidget {
  const StaggeredDotsAvatarLoader({super.key, required this.image});
  final ImageProvider image;

  @override
  State<StaggeredDotsAvatarLoader> createState() => _StaggeredDotsAvatarLoaderState();
}

class _StaggeredDotsAvatarLoaderState extends State<StaggeredDotsAvatarLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _dotAnimations;
  final int _numDots = 5;
  final double _animationDuration = 5.0; // Total animation cycle in seconds

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(seconds: _animationDuration.toInt()),
      vsync: this,
    )..repeat();

    // Create sequential animations for each dot
    _dotAnimations = List.generate(_numDots, (index) {
      // Each dot gets 1/numDots of the total animation time
      final double startPoint = index / _numDots;
      final double endPoint = (index + 1) / _numDots;

      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(startPoint, endPoint, curve: Curves.easeInOut),
        ),
      );
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Orbit path (faded circle)
          Container(
            width: 180,
            height: 180,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.primary.withAlpha(50),
                width: 2,
              ),
            ),
          ),

          // Central avatar
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.neutralLight,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withAlpha(50),
                  blurRadius: 15,
                  spreadRadius: 2,
                ),
              ],
              image: DecorationImage(
                image: widget.image,
                fit: BoxFit.contain,
              ),
            ),
          ),

          // Animated dots
          ...List.generate(_numDots, (index) {
            return _buildAnimatedDot(index);
          }),
        ],
      ),
    );
  }

  Widget _buildAnimatedDot(int index) {
    return AnimatedBuilder(
      animation: _dotAnimations[index],
      builder: (context, child) {
        // Calculate the position on the circle
        final double angle = 2 * pi * _dotAnimations[index].value - (pi / 2);
        final double radius = 90; // Half of the container size

        // Calculate dot position
        final double x = radius * cos(angle);
        final double y = radius * sin(angle);

        // Determine if this dot is currently active
        final bool isActive = _controller.value >= index / _numDots &&
                             _controller.value < (index + 1) / _numDots;

        // Determine the color based on activity
        final Color dotColor = isActive
            ? AppColors.accent
            : _controller.value > (index + 1) / _numDots
                ? AppColors.secondary
                : AppColors.primary.withAlpha(100);

        // Determine the size based on activity
        final double dotSize = isActive ? 12.0 : 8.0;

        // Create a stack with trail effect for active dots
        if (isActive) {
          // Create trail dots with decreasing opacity
          List<Widget> trailDots = [];
          final int trailCount = 5;

          for (int i = 1; i <= trailCount; i++) {
            // Calculate trail position (behind the main dot)
            final double trailProgress = _dotAnimations[index].value - (i * 0.03);
            if (trailProgress < 0) continue;

            final double trailAngle = 2 * pi * trailProgress - (pi / 2);
            final double trailX = radius * cos(trailAngle);
            final double trailY = radius * sin(trailAngle);

            // Decreasing size and opacity for trail dots
            final double trailSize = dotSize * (1 - (i / (trailCount + 2)));
            final int trailAlpha = (100 * (1 - (i / trailCount))).toInt();

            trailDots.add(
              Transform.translate(
                offset: Offset(trailX, trailY),
                child: Container(
                  width: trailSize,
                  height: trailSize,
                  decoration: BoxDecoration(
                    color: AppColors.accent.withAlpha(trailAlpha),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            );
          }

          // Add the main dot at the end so it appears on top
          trailDots.add(
            Transform.translate(
              offset: Offset(x, y),
              child: Container(
                width: dotSize,
                height: dotSize,
                decoration: BoxDecoration(
                  color: dotColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.accent.withAlpha(100),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
              ),
            ),
          );

          return Stack(children: trailDots);
        } else {
          // For inactive dots, just return the dot without trail
          return Transform.translate(
            offset: Offset(x, y),
            child: Container(
              width: dotSize,
              height: dotSize,
              decoration: BoxDecoration(
                color: dotColor,
                shape: BoxShape.circle,
                boxShadow: _controller.value > (index + 1) / _numDots
                    ? [
                        BoxShadow(
                          color: AppColors.secondary.withAlpha(50),
                          blurRadius: 5,
                          spreadRadius: 1,
                        ),
                      ]
                    : null,
              ),
            ),
          );
        }
      },
    );
  }
}