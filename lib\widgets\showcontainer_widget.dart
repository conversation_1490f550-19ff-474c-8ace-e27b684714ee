import 'package:flutter/material.dart';

class ShowcontainerWidget extends StatelessWidget {
  final double height;
  final double width;
  final Color color;
  final Widget child;

  const ShowcontainerWidget({
    super.key,
    required this.height,
    required this.width,
    required this.color,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(20),
      ),
      child: child,
    );
  }
}
