import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hotel_booking/models/hotel_rooms.dart';

class RoomSelectionProvider with ChangeNotifier{
  Hotelrooms? _hotelrooms;
  List<Room> _rooms = [];
  String _selectedSortOption = 'all';

  List<Room> get rooms => _rooms;
  Hotelrooms? get hotelrooms => _hotelrooms;
    String get selectedSortOption => _selectedSortOption;


  Future<void> loadHotelRoomsFromJson() async {
    try {
      final String jsonString = await rootBundle.loadString('assets/json/hotelleveldetails.json');
      _hotelrooms = hotelroomsFromJson(jsonString);
      _rooms = _hotelrooms?.data?.rooms ?? [];

      notifyListeners();
    } catch (e) {
      print('Failed to load hotel rooms: $e');
    }
  }

    void setSortOption(String option) {
    _selectedSortOption = option;
    notifyListeners();
  }

 List<RoomOption> getFilteredRoomOptions(Room room) {
    if (_selectedSortOption == 'all') {
      return room.roomOptions ?? [];
    }
    
    return (room.roomOptions ?? []).where((option) {
      switch (_selectedSortOption) {
        case 'free_breakfast':
          return option.mealBenefits?.any((benefit) => 
            benefit.toLowerCase().contains('breakfast')) ?? false;
        case 'free_parking_wifi_breakfast':
          return (option.mealBenefits?.any((benefit) => 
            benefit.toLowerCase().contains('breakfast')) ?? false) &&
            (option.otherBenefits?.any((benefit) => 
              benefit.toLowerCase().contains('parking') || 
              benefit.toLowerCase().contains('wifi')) ?? false);
        case 'breakfast':
          return option.mealBenefits?.any((benefit) => 
            benefit.toLowerCase().contains('breakfast')) ?? false;  
        case 'bed_and_breakfast':
          return option.name?.toLowerCase().contains('bed') ?? false ||
            (option.mealBenefits?.any((benefit) => 
              benefit.toLowerCase().contains('breakfast')) ?? false);
        case 'free_parking_wifi_room':
          return option.otherBenefits?.any((benefit) => 
            benefit.toLowerCase().contains('parking') || 
            benefit.toLowerCase().contains('wifi')) ?? false;
        case 'room_only':
          return (option.mealBenefits?.isEmpty ?? true) ||
            (option.name?.toLowerCase().contains('room only') ?? false);
        default:
          return true;
      }
    }).toList();
  }

}