import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_text_styles.dart';
import 'package:hotel_booking/constants/app_dimensions.dart';
import 'package:hotel_booking/providers/profile_provider.dart';
import 'package:hotel_booking/screens/profile screen/widgets/booking_history_widget.dart';
import 'package:hotel_booking/screens/profile screen/widgets/personal_information_widget.dart';
import 'package:hotel_booking/screens/profile screen/widgets/upcoming_bookings_widget.dart';
import 'package:hotel_booking/screens/wishlist screen/wishlist_screen.dart';
import 'package:hotel_booking/screens/language screen/language_screen.dart';
import 'package:hotel_booking/helper%20functions/string_extention_helper.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ProfileProvider(),
      child: _ProfileScreenContent(),
    );
  }
}

class _ProfileScreenContent extends StatefulWidget {
  @override
  _ProfileScreenContentState createState() => _ProfileScreenContentState();
}

class _ProfileScreenContentState extends State<_ProfileScreenContent> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _profileCardAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _profileCardAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _animationController.forward();

    // Load profile data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<ProfileProvider>(context, listen: false);
      provider.loadUserProfile();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        title: Text(
          'profile.profile'.tr,
          style: AppTextStyles.headline2.copyWith(color: Colors.white),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_rounded, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.white),
            onPressed: () {
              // Navigate to edit profile page
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PersonalInformationWidget(),
                ),
              );
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.background,
              AppColors.neutralLight.withAlpha(100),
              Colors.white,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: Consumer<ProfileProvider>(
          builder: (context, provider, child) {
            if (provider.isLoading) {
              return const Center(
                child: CircularProgressIndicator(
                  color: AppColors.accent,
                ),
              );
            }

            if (provider.error != null) {
              return _buildErrorState(provider);
            }

            return SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children: [
                  _buildProfileCard(provider),
                  _buildSectionTitle('Account Settings'),
                  _buildSettingsSection(),
                  _buildSectionTitle('Booking Information'),
                  _buildBookingSection(provider),
                  _buildSectionTitle('Support & Help'),
                  _buildSupportSection(),
                  SizedBox(height: AppDimensions.paddingSizeLarge),
                  _buildLogoutButton(),
                  SizedBox(height: AppDimensions.paddingSizeLarge * 2),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileCard(ProfileProvider provider) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, -0.2),
        end: Offset.zero,
      ).animate(_profileCardAnimation),
      child: FadeTransition(
        opacity: _profileCardAnimation,
        child: Container(
          margin: EdgeInsets.all(AppDimensions.paddingSizeDefault),
          padding: EdgeInsets.all(AppDimensions.paddingSizeLarge),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withAlpha(230),
                Colors.white.withAlpha(180),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.white.withAlpha(100),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withAlpha(25),
                spreadRadius: 0,
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
              BoxShadow(
                color: Colors.white.withAlpha(200),
                spreadRadius: -5,
                blurRadius: 15,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Column(
            children: [
              Stack(
                children: [
                  Container(
                    padding: EdgeInsets.all(AppDimensions.paddingSizeSmall),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.primary.withAlpha(200),
                          AppColors.secondary.withAlpha(200),
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withAlpha(100),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: CircleAvatar(
                      radius: 50,
                      backgroundColor: Colors.white,
                      backgroundImage: provider.userProfile.profileImage != null
                          ? NetworkImage(provider.userProfile.profileImage!)
                          : null,
                      child: provider.userProfile.profileImage == null
                          ? Icon(
                              Icons.person,
                              size: 50,
                              color: AppColors.primary,
                            )
                          : null,
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.accent,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.accent.withAlpha(100),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.verified,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppDimensions.paddingSizeDefault),
              Text(
                provider.userProfile.name ?? 'User Name',
                style: AppTextStyles.headline3,
              ),
              SizedBox(height: AppDimensions.paddingSizeExtraSmall),
              Text(
                provider.userProfile.email ?? '<EMAIL>',
                style: AppTextStyles.bodyText1.copyWith(
                  color: AppColors.textLight,
                ),
              ),
              SizedBox(height: AppDimensions.paddingSizeDefault),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.star,
                    color: Colors.amber,
                    size: 20,
                  ),
                  SizedBox(width: 4),
                  Text(
                    '${provider.userProfile.membershipLevel ?? 'Standard'} Member',
                    style: AppTextStyles.subtitle2.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.secondary,
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppDimensions.paddingSizeDefault),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildProfileStat(
                    '${provider.userProfile.totalBookings ?? 0}',
                    'Bookings',
                    Icons.book_online_rounded,
                  ),
                  _buildDivider(),
                  _buildProfileStat(
                    '${provider.userProfile.rewardPoints ?? 0}',
                    'Points',
                    Icons.card_giftcard_rounded,
                  ),
                  _buildDivider(),
                  _buildProfileStat(
                    '${provider.userProfile.completedStays ?? 0}',
                    'Stays',
                    Icons.home_work_rounded,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileStat(String value, String label, IconData icon) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(AppDimensions.paddingSizeSmall),
          decoration: const BoxDecoration(
            color: Color.fromRGBO(0, 122, 255, 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 24,
          ),
        ),
        SizedBox(height: AppDimensions.paddingSizeExtraSmall),
        Text(
          value,
          style: AppTextStyles.headline1.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(
            color: AppColors.textLight,
            fontSize: AppDimensions.fontSizeSmall,
          ),
        ),
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 40,
      width: 1,
      color: const Color.fromRGBO(128, 128, 128, 0.2),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        AppDimensions.paddingSizeDefault,
        AppDimensions.paddingSizeLarge,
        AppDimensions.paddingSizeDefault,
        AppDimensions.paddingSizeSmall,
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.accent,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          SizedBox(width: AppDimensions.paddingSizeSmall),
          Text(
            title,
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection() {
    final settingsItems = [
      {
        'title': 'Personal Information',
        'icon': Icons.person_outline,
        'iconColor': AppColors.primary,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PersonalInformationWidget(),
            ),
          );
        },
      },
      {
        'title': 'Change Your Language',
        'icon': Icons.translate_rounded,
        'iconColor': AppColors.secondary,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const LanguageScreen(),
            ),
          );
        },
      },
      {
        'title': 'Payment Methods',
        'icon': Icons.credit_card,
        'iconColor': AppColors.accent,
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Payment Methods feature coming soon'),
              backgroundColor: AppColors.primary,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
          );
        },
      },
      {
        'title': 'Notification Settings',
        'icon': Icons.notifications_none_rounded,
        'iconColor': Colors.orange,
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Notification Settings feature coming soon'),
              backgroundColor: AppColors.primary,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
          );
        },
      },
      {
        'title': 'profile.wishlist'.tr,
        'icon': Icons.favorite_outline,
        'iconColor': Colors.red,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const WishlistScreen(),
            ),
          );
        },
      },
    ];

    return _buildSettingsCards(settingsItems);
  }

  Widget _buildBookingSection(ProfileProvider provider) {
    final bookingItems = [
      {
        'title': 'Upcoming Bookings',
        'icon': Icons.upcoming,
        'badge': provider.userProfile.upcomingBookings?.toString() ?? '0',
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const UpcomingBookingsWidget(),
            ),
          );
        },
      },
      {
        'title': 'Booking History',
        'icon': Icons.history,
        'badge': null,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const BookingHistoryWidget(),
            ),
          );
        },
      },
      {
        'title': 'My Reviews',
        'icon': Icons.star_border_rounded,
        'badge': provider.userProfile.reviewsCount?.toString() ?? '0',
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('My Reviews feature coming soon')),
          );
        },
      },
    ];

    return _buildSettingsCards(bookingItems);
  }

  Widget _buildSupportSection() {
    final supportItems = [
      {
        'title': 'Help Center',
        'icon': Icons.help_outline,
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Help Center feature coming soon')),
          );
        },
      },
      {
        'title': 'Contact Us',
        'icon': Icons.support_agent,
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Contact Us feature coming soon')),
          );
        },
      },
      {
        'title': 'Terms & Conditions',
        'icon': Icons.description_outlined,
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Terms & Conditions feature coming soon')),
          );
        },
      },
    ];

    return _buildSettingsCards(supportItems);
  }

  Widget _buildSettingsCards(List<Map<String, dynamic>> items) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: AppDimensions.paddingSizeDefault),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withAlpha(230),
            Colors.white.withAlpha(180),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withAlpha(100),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withAlpha(15),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          BoxShadow(
            color: Colors.white.withAlpha(200),
            spreadRadius: -5,
            blurRadius: 15,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: items.length,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          color: AppColors.textLight.withAlpha(25),
          indent: AppDimensions.paddingSizeDefault * 2 + 24, // Indent to align with text
        ),
        itemBuilder: (context, index) {
          final item = items[index];
          final iconColor = item['iconColor'] as Color? ?? AppColors.primary;
          return ListTile(
            onTap: item['onTap'] as Function(),
            contentPadding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingSizeDefault,
              vertical: AppDimensions.paddingSizeExtraSmall,
            ),
            leading: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    iconColor.withAlpha(50),
                    iconColor.withAlpha(25),
                  ],
                ),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: iconColor.withAlpha(100),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: iconColor.withAlpha(50),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                item['icon'] as IconData,
                color: iconColor,
                size: 24,
              ),
            ),
            title: Text(
              item['title'] as String,
              style: AppTextStyles.bodyText1.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (item['badge'] != null)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingSizeSmall,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.accent,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      item['badge'] as String,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: AppDimensions.fontSizeExtraSmall,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                SizedBox(width: item['badge'] != null ? AppDimensions.paddingSizeSmall : 0),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.textLight,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingSizeLarge,
      ),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          _showLogoutDialog();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.red,
          padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingSizeDefault + 4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
            side: BorderSide(color: Colors.red.withAlpha(100), width: 1.5),
          ),
          elevation: 0,
        ).copyWith(
          backgroundColor: WidgetStateProperty.resolveWith<Color>(
            (Set<WidgetState> states) {
              if (states.contains(WidgetState.pressed)) {
                return Colors.red.withAlpha(25);
              }
              return Colors.transparent;
            },
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.logout_rounded,
              color: Colors.red,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'profile.logout'.tr,
              style: AppTextStyles.subtitle1.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Icon(Icons.logout_rounded, color: Colors.red),
            const SizedBox(width: 12),
            Text('Confirm Logout'),
          ],
        ),
        content: Text('Are you sure you want to logout from your account?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Logout functionality coming soon'),
                  backgroundColor: AppColors.primary,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: Text('Logout'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(ProfileProvider provider) {
    return Center(
      child: Container(
        margin: EdgeInsets.all(AppDimensions.paddingSizeLarge),
        padding: EdgeInsets.all(AppDimensions.paddingSizeLarge),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppDimensions.radiusExtraLarge),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              spreadRadius: 1,
              blurRadius: 10,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            SizedBox(height: AppDimensions.paddingSizeDefault),
            Text(
              'Error loading profile',
              style: AppTextStyles.subtitle1.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppDimensions.paddingSizeSmall),
            Text(
              provider.error!,
              style: AppTextStyles.bodyText2,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.paddingSizeLarge),
            ElevatedButton(
              onPressed: provider.loadUserProfile,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accent,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingSizeLarge,
                  vertical: AppDimensions.paddingSizeSmall
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }}