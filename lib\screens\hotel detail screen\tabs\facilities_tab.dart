import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';

class FacilitiesTab extends StatefulWidget {
  const FacilitiesTab({Key? key}) : super(key: key);

  @override
  State<FacilitiesTab> createState() => _FacilitiesTabState();
}

class _FacilitiesTabState extends State<FacilitiesTab> {
  // Currently selected facility section
  int _selectedIndex = 0;

  // Facility data
  final List<FacilitySection> _facilitySections = [
    FacilitySection(
      title: 'Room amenities',
      icon: Icons.hotel,
      items: [
        'Air-conditioned rooms',
        'Daily housekeeping',
        'Free drinking water',
        'Hot/Cold water',
        'Tea/Coffee maker',
        'Safety Deposit Box',
      ],
    ),
    FacilitySection(
      title: 'Internet Access',
      icon: Icons.wifi,
      items: [
        'Internet services',
        'Free Wi-Fi',
      ],
    ),
    FacilitySection(
      title: 'Food and drinks',
      icon: Icons.restaurant,
      items: [
        'Continental Breakfast',
        '24/7 Room Service',
        'Bar',
        'Coffee Shop',
        'In-Room Breakfast',
      ],
    ),
    FacilitySection(
      title: 'Activities and sports',
      icon: Icons.sports_tennis,
      items: [
        'Indoor Swimming Pool',
        'Fitness Center',
        'Spa/Sauna',
        'Bicycle Rental',
      ],
    ),
    FacilitySection(
      title: 'Services and \nconveniences',
      icon: Icons.luggage,
      items: [
        'Facilities for Differently-Abled Guests',
        'FLuggage Storage',
        'Laundry Service',
        'Chapel',
        'Doorman',
        'Dry-Cleaning',
        'Library',
        'Postal Service',
      ],
    ),
    FacilitySection(
      title: 'Safety and cleanliness',
      icon: Icons.cleaning_services,
      items: [
        'Cashless Payment Service',
        'Common Area Disinfection (Daily)',
        'Doctor/Nurse on Call',
        'Free Face Masks',
        'No Shared Stationery',
      ],
    ),
    FacilitySection(
      title: 'Kitchen',
      icon: Icons.coffee,
      items: [
        'Tea/Coffee maker',
      ],
    ),
    FacilitySection(
      title: 'Bathroom',
      icon: Icons.shower,
      items: [
        'Shower',
      ],
    ),
    FacilitySection(
      title: 'Accessibility',
      icon: Icons.elevator,
      items: [
        'Elevator access',
      ],
    ),
    FacilitySection(
      title: 'Transfers and \ntransport',
      icon: Icons.airport_shuttle,
      items: [
        'Onsite car parking',
        'Airport transfer',
        'Car rental service',
        'Shuttle service',
        'Taxi/Cab service',
      ],
    ),
    FacilitySection(
      title: 'Access',
      icon: Icons.desktop_windows,
      items: [
        '24*7 Front Desk',
        'Non-smoking rooms',
        'Private Check-in/out',
      ],
    ),
    FacilitySection(
      title: 'Safety and security',
      icon: Icons.videocam,
      items: [
        '24*7 Security',
        'CCTV in common areas',
        'Fire extinguisher',
      ],
    ),
    FacilitySection(
      title: 'Languages spoken',
      icon: Icons.language,
      items: [
        'English',
        'Hindi',
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height *
          0.8, // Set a specific height constraint
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // _buildSectionTitle('Facilities'),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left sidebar with facility icons
                _buildFacilityIconsSidebar(),
                // Right content area showing selected facility details
                Expanded(
                  child: _buildFacilityDetailsContainer(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

Widget _buildFacilityIconsSidebar() {
  return Container(
    width: 80,
    padding: const EdgeInsets.symmetric(vertical: 16),
    decoration: BoxDecoration(
      color: Colors.grey[100],
      borderRadius: const BorderRadius.only(
        topRight: Radius.circular(20),
        bottomRight: Radius.circular(20),
      ),
    ),
    child: Stack(
      children: [
        // Scrollable icons
        SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(_facilitySections.length, (index) {
              return _buildFacilityIconItem(index);
            }),
          ),
        ),
        // Arrow at bottom
        Align(
          alignment: Alignment.bottomRight,
          child: Container(
            padding: const EdgeInsets.only(bottom: 8),
            child: Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.background,
              size: 40,
            ),
          ),
        ),
      ],
    ),
  );
}


Widget _buildFacilityIconItem(int index) {
  final bool isSelected = index == _selectedIndex;
  final FacilitySection section = _facilitySections[index];

  return InkWell(
    onTap: () {
      setState(() {
        _selectedIndex = index;
      });
    },
    child: Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primary : Colors.transparent,
        borderRadius: isSelected
            ? const BorderRadius.only(
                topRight: Radius.circular(16),
                bottomRight: Radius.circular(16),
              )
            : BorderRadius.zero,
      ),
      child: Column(
        children: [
          Icon(
            section.icon,
            color: isSelected ? Colors.white : AppColors.secondary,
            size: 28,
          ),
          const SizedBox(height: 6),
          Text(
            section.title.split(' ')[0],
            style: TextStyle(
              color: isSelected ? Colors.white : AppColors.textLight,
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ),
  );
}


  Widget _buildFacilityDetailsContainer() {
    final FacilitySection selectedSection = _facilitySections[_selectedIndex];

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                selectedSection.icon,
                color: AppColors.primary,
                size: 30,
              ),
              const SizedBox(width: 16),
              Text(
                selectedSection.title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const Divider(height: 32),
          Expanded(
            child: ListView.builder(
              itemCount: selectedSection.items.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.check_circle,
                        size: 18,
                        color: AppColors.secondary,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          selectedSection.items[index],
                          style: const TextStyle(fontSize: 15),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Model class to hold facility data
class FacilitySection {
  final String title;
  final IconData icon;
  final List<String> items;

  FacilitySection({
    required this.title,
    required this.icon,
    required this.items,
  });
}
