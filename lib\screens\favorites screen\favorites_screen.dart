import 'package:flutter/material.dart';

import 'package:hotel_booking/models/hotel_details.dart';
import 'package:provider/provider.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_text_styles.dart';
import 'package:hotel_booking/constants/app_dimensions.dart';
import 'package:hotel_booking/providers/favorites_provider.dart';
import 'package:hotel_booking/screens/hotel%20list%20screen/widgets/loading_indicator.dart';

class FavoritesScreen extends StatelessWidget {
  const FavoritesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => FavoritesProvider(),
      child: _FavoritesScreenContent(),
    );
  }
}

class _FavoritesScreenContent extends StatefulWidget {
  @override
  _FavoritesScreenContentState createState() => _FavoritesScreenContentState();
}

class _FavoritesScreenContentState extends State<_FavoritesScreenContent> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _headerAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _headerAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    _animationController.forward();
    
    // Load favorites when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<FavoritesProvider>(context, listen: false);
      provider.loadFavorites();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        title: Text(
          'My Favorites',
          style: AppTextStyles.headline2.copyWith(color: Colors.white),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_rounded),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primary,
              Colors.white,
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: Column(
          children: [
            _buildHeader(context),
            Expanded(
              child: _buildFavoritesList(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final provider = Provider.of<FavoritesProvider>(context);
    
    return FadeTransition(
      opacity: _headerAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, -0.2),
          end: Offset.zero,
        ).animate(_headerAnimation),
        child: Container(
          margin: EdgeInsets.fromLTRB(
            AppDimensions.paddingSizeDefault,
            AppDimensions.paddingSizeSmall,
            AppDimensions.paddingSizeDefault,
            AppDimensions.paddingSizeLarge
          ),
          padding: EdgeInsets.all(AppDimensions.paddingSizeLarge),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppDimensions.radiusExtraLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                spreadRadius: 1,
                blurRadius: 10,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(AppDimensions.paddingSizeSmall),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
                    ),
                    child: Icon(
                      Icons.favorite_rounded,
                      color: AppColors.accent,
                      size: 28,
                    ),
                  ),
                  SizedBox(width: AppDimensions.paddingSizeDefault),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Your Saved Hotels',
                          style: AppTextStyles.headline3,
                        ),
                        SizedBox(height: 4),
                        Consumer<FavoritesProvider>(
                          builder: (context, provider, child) {
                            return Text(
                              '${provider.favorites.length} hotel${provider.favorites.length != 1 ? 's' : ''} saved',
                              style: AppTextStyles.bodyText1.copyWith(
                                color: AppColors.textLight,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppDimensions.paddingSizeDefault),
              Text(
                'View your wishlist of hotels that you have liked and want to book in the future.',
                style: AppTextStyles.bodyText2.copyWith(
                  color: AppColors.textLight,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFavoritesList(BuildContext context) {
    return Consumer<FavoritesProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const LoadingIndicator();
        }

        if (provider.error != null) {
          return _buildErrorState(provider);
        }

        if (provider.favorites.isEmpty) {
          return _buildEmptyState();
        }

        return Container(
          margin: EdgeInsets.only(top: AppDimensions.paddingSizeSmall),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(AppDimensions.radiusExtraLarge),
              topRight: Radius.circular(AppDimensions.radiusExtraLarge),
            ),
          ),
          child: ListView.separated(
            padding: EdgeInsets.fromLTRB(
              AppDimensions.paddingSizeDefault,
              AppDimensions.paddingSizeLarge,
              AppDimensions.paddingSizeDefault,
              AppDimensions.paddingSizeLarge,
            ),
            itemCount: provider.favorites.length,
            physics: const BouncingScrollPhysics(),
            itemBuilder: (context, index) {
           final hotel = provider.favorites[index];
  return _buildFavoriteCard(context, hotel);
          
            },
            separatorBuilder: (context, index) => SizedBox(height: AppDimensions.paddingSizeDefault),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Container(
        margin: EdgeInsets.all(AppDimensions.paddingSizeLarge),
        padding: EdgeInsets.all(AppDimensions.paddingSizeLarge),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppDimensions.radiusExtraLarge),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 1,
              blurRadius: 10,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.favorite_border_rounded,
              size: 80,
              color: AppColors.textLight.withOpacity(0.5),
            ),
            SizedBox(height: AppDimensions.paddingSizeLarge),
            Text(
              'No Favorites Yet',
              style: AppTextStyles.headline3,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.paddingSizeDefault),
            Text(
              'Start saving your favorite hotels by tapping the heart icon on any hotel card.',
              style: AppTextStyles.bodyText1.copyWith(
                color: AppColors.textLight,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.paddingSizeLarge),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.secondary,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingSizeLarge,
                  vertical: AppDimensions.paddingSizeDefault,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
                ),
              ),
              child: Text(
                'Explore Hotels',
                style: AppTextStyles.subtitle1.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(FavoritesProvider provider) {
    return Center(
      child: Container(
        margin: EdgeInsets.all(AppDimensions.paddingSizeLarge),
        padding: EdgeInsets.all(AppDimensions.paddingSizeLarge),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppDimensions.radiusExtraLarge),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 1,
              blurRadius: 10,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            SizedBox(height: AppDimensions.paddingSizeDefault),
            Text(
              'Error loading favorites',
              style: AppTextStyles.subtitle1.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppDimensions.paddingSizeSmall),
            Text(
              provider.error!,
              style: AppTextStyles.bodyText2,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.paddingSizeLarge),
            ElevatedButton(
              onPressed: provider.loadFavorites,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accent,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingSizeLarge,
                  vertical: AppDimensions.paddingSizeSmall
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}

Widget _buildFavoriteCard(BuildContext context, InventoryInfoList hotel) {
  return Container(
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.05),
          blurRadius: 10,
          offset: Offset(0, 3),
        ),
      ],
    ),
    child: Row(
      children: [
   
        Expanded(
          child: Padding(
            padding: EdgeInsets.all(AppDimensions.paddingSizeDefault),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  hotel.name.toString(),
                  style: AppTextStyles.headline1.copyWith(fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Text(
                  hotel.locality.toString(),
                  style: AppTextStyles.bodyText2.copyWith(color: AppColors.textLight),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 6),
                Row(
                  children: [
                    Icon(Icons.star, size: 16, color: Colors.amber),
                    SizedBox(width: 4),
                    Text(
                      '${hotel.userRating}',
                      style: AppTextStyles.bodyText1,
                    ),
                    Spacer(),
                    Text(
                      '\$${hotel.fareDetail!.totalPrice.toString()}/night',
                      style: AppTextStyles.bodyText1.copyWith(
                        color: AppColors.accent,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    ),
  );
}
