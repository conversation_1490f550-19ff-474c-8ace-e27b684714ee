import 'package:flutter/material.dart';

class SearchableDropdown<T> extends StatefulWidget {
  final List<T> items;
  final T? value;
  final String Function(T) itemToString;
  final ValueChanged<T> onChanged;
  final String hintText;

  const SearchableDropdown({
    super.key,
    required this.items,
    required this.onChanged,
    required this.itemToString,
    this.value,
    this.hintText = 'Select job role',
  });

  @override
  State<SearchableDropdown<T>> createState() => _SearchableDropdownState<T>();
}

class _SearchableDropdownState<T> extends State<SearchableDropdown<T>> {
  late List<T> filteredItems;
  final TextEditingController _searchController = TextEditingController();

  void _openDropdownSheet() {
    filteredItems = List<T>.from(widget.items);
    _searchController.clear();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Search Field
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                  setModalState(() {
                                    filteredItems = widget.items;
                                  });
                                },
                              )
                            : null,
                        hintText: 'Search',
                        border: InputBorder.none,
                      ),
                      onChanged: (query) {
                        setModalState(() {
                          filteredItems = widget.items
                              .where((item) => widget
                                  .itemToString(item)
                                  .toLowerCase()
                                  .contains(query.toLowerCase()))
                              .toList();
                        });
                      },
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Scrollable List
                  Flexible(
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(maxHeight: 300),
                      child: Scrollbar(
                        child: ListView.separated(
                          itemCount: filteredItems.length,
                          itemBuilder: (context, index) {
                            final item = filteredItems[index];
                            return ListTile(
                              title: Text(widget.itemToString(item)),
                              onTap: () {
                                widget.onChanged(item);
                                Navigator.pop(context);
                              },
                            );
                          },
                          separatorBuilder: (_, __) =>
                              const Divider(height: 1),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _openDropdownSheet,
      child: InputDecorator(
        decoration: InputDecoration(
          hintText: widget.hintText,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          suffixIcon: const Icon(Icons.arrow_drop_down),
        ),
        child: Text(
          widget.value != null ? widget.itemToString(widget.value!) : '',
          style: TextStyle(
            color: widget.value != null ? Colors.black : Colors.grey.shade600,
          ),
        ),
      ),
    );
  }
}
