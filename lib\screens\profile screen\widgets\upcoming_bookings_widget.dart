import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_text_styles.dart';
import 'package:hotel_booking/screens/itinerary%20screen/itinerary_screen.dart';

class UpcomingBookingsWidget extends StatefulWidget {
  const UpcomingBookingsWidget({super.key});

  @override
  State<UpcomingBookingsWidget> createState() => _UpcomingBookingsWidgetState();
}

class _UpcomingBookingsWidgetState extends State<UpcomingBookingsWidget> {
  // Sample booking data - in a real app, this would come from an API or database
  final List<Map<String, dynamic>> _upcomingBookings = [
    {
      'id': 'BK78945612',
      'hotelName': 'Luxury Resort & Spa',
      'location': 'Dubai, UAE',
      'roomType': 'Deluxe Room',
      'checkInDate': 'May 15, 2023',
      'checkOutDate': 'May 18, 2023',
      'numberOfGuests': 2,
      'totalAmount': 400.0,
      'status': 'Confirmed',
      'imageUrl': 'assets/images/sara-dubler-Koei_7yYtIo-unsplash.jpg',
      'daysLeft': 5,
    },
    {
      'id': 'BK78945613',
      'hotelName': 'Grand Plaza Hotel',
      'location': 'New York, USA',
      'roomType': 'Executive Suite',
      'checkInDate': 'Jun 10, 2023',
      'checkOutDate': 'Jun 15, 2023',
      'numberOfGuests': 2,
      'totalAmount': 750.0,
      'status': 'Pending',
      'imageUrl': 'assets/images/sara-dubler-Koei_7yYtIo-unsplash.jpg',
      'daysLeft': 31,
    },
    {
      'id': 'BK78945614',
      'hotelName': 'Beachfront Resort',
      'location': 'Phuket, Thailand',
      'roomType': 'Ocean View Suite',
      'checkInDate': 'Jul 20, 2023',
      'checkOutDate': 'Jul 25, 2023',
      'numberOfGuests': 3,
      'totalAmount': 620.0,
      'status': 'Confirmed',
      'imageUrl': 'assets/images/sara-dubler-Koei_7yYtIo-unsplash.jpg',
      'daysLeft': 71,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upcoming Bookings'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _upcomingBookings.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _upcomingBookings.length,
              itemBuilder: (context, index) {
                final booking = _upcomingBookings[index];
                return _buildBookingCard(booking);
              },
            ),
    );
  }

  Widget _buildBookingCard(Map<String, dynamic> booking) {
    Color statusColor;
    IconData statusIcon;
    
    switch (booking['status']) {
      case 'Confirmed':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'Pending':
        statusColor = Colors.orange;
        statusIcon = Icons.access_time;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.info;
    }
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // Navigate to booking details
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ItineraryScreen(
                bookingId: booking['id'],
                hotelName: booking['hotelName'],
                roomType: booking['roomType'],
                checkInDate: booking['checkInDate'],
                checkOutDate: booking['checkOutDate'],
                numberOfGuests: booking['numberOfGuests'],
                totalAmount: booking['totalAmount'],
                guestName: 'John Doe',
                guestEmail: '<EMAIL>',
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            // Countdown banner
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${booking['checkInDate']} - ${booking['checkOutDate']}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(30),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.access_time_filled,
                          color: Colors.white,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${booking['daysLeft']} days left',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Hotel image
            SizedBox(
              height: 150,
              width: double.infinity,
              child: Image.asset(
                booking['imageUrl'],
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  height: 150,
                  width: double.infinity,
                  color: Colors.grey.shade300,
                  child: const Icon(
                    Icons.image,
                    size: 50,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
            
            // Booking details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Hotel name and status
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          booking['hotelName'],
                          style: AppTextStyles.headline3.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: statusColor.withAlpha(30),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              statusIcon,
                              color: statusColor,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              booking['status'],
                              style: TextStyle(
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // Location
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        booking['location'],
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  // Room details
                  Row(
                    children: [
                      _buildDetailItem(
                        Icons.hotel,
                        'Room Type',
                        booking['roomType'],
                      ),
                      _buildDetailItem(
                        Icons.person,
                        'Guests',
                        '${booking['numberOfGuests']} Guests',
                      ),
                      _buildDetailItem(
                        Icons.attach_money,
                        'Total',
                        '\$${booking['totalAmount'].toStringAsFixed(2)}',
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 8),
                  
                  // Action buttons
                  Row(
                    children: [
                      _buildActionButton(
                        'Modify',
                        Icons.edit,
                        Colors.blue,
                        () {
                          // Handle modify booking
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Modify booking...')),
                          );
                        },
                      ),
                      const SizedBox(width: 16),
                      _buildActionButton(
                        'Cancel',
                        Icons.cancel,
                        Colors.red,
                        () {
                          // Handle cancel booking
                          _showCancelDialog(booking);
                        },
                      ),
                      const Spacer(),
                      _buildActionButton(
                        'View Details',
                        Icons.arrow_forward,
                        AppColors.primary,
                        () {
                          // Navigate to booking details
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ItineraryScreen(
                                bookingId: booking['id'],
                                hotelName: booking['hotelName'],
                                roomType: booking['roomType'],
                                checkInDate: booking['checkInDate'],
                                checkOutDate: booking['checkOutDate'],
                                numberOfGuests: booking['numberOfGuests'],
                                totalAmount: booking['totalAmount'],
                                guestName: 'John Doe',
                                guestEmail: '<EMAIL>',
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDetailItem(IconData icon, String title, String value) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 14,
                color: Colors.grey,
              ),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 13,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
  
  Widget _buildActionButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              text,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_available,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'You have no upcoming bookings',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              // Navigate to hotel search
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Book a Hotel'),
          ),
        ],
      ),
    );
  }
  
  void _showCancelDialog(Map<String, dynamic> booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to cancel your booking at ${booking['hotelName']}?'),
            const SizedBox(height: 16),
            const Text(
              'Cancellation Policy:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '• Free cancellation until 48 hours before check-in\n'
              '• If you cancel within 48 hours of check-in, the first night will be charged',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('No, Keep Booking'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Handle cancel booking
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Booking cancelled successfully')),
              );
              
              // In a real app, you would call an API to cancel the booking
              // and then update the UI accordingly
              setState(() {
                _upcomingBookings.removeWhere((b) => b['id'] == booking['id']);
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Yes, Cancel Booking'),
          ),
        ],
      ),
    );
  }
}
