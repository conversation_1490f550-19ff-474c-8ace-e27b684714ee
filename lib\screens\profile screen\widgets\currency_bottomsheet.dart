import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_text_styles.dart';
import 'package:hotel_booking/constants/app_dimensions.dart';
import 'package:hotel_booking/constants/app_localizations.dart';
import 'package:hotel_booking/providers/currency_provider.dart';
import 'package:provider/provider.dart';

class CurrencyBottomsheet extends StatelessWidget {
  const CurrencyBottomsheet({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Map<String, String>> currencies = [
      {'code': 'USD', 'name': 'US Dollar', 'symbol': '\$', 'flag': '🇺🇸'},
      {'code': 'EUR', 'name': 'Euro', 'symbol': '€', 'flag': '🇪🇺'},
      {'code': 'GBP', 'name': 'British Pound', 'symbol': '£', 'flag': '🇬🇧'},
      {'code': 'JPY', 'name': 'Japanese Yen', 'symbol': '¥', 'flag': '🇯🇵'},
      {'code': 'AUD', 'name': 'Australian Dollar', 'symbol': 'A\$', 'flag': '🇦🇺'},
      {'code': 'CAD', 'name': 'Canadian Dollar', 'symbol': 'C\$', 'flag': '🇨🇦'},
      {'code': 'CHF', 'name': 'Swiss Franc', 'symbol': 'CHF', 'flag': '🇨🇭'},
      {'code': 'CNY', 'name': 'Chinese Yuan', 'symbol': '¥', 'flag': '🇨🇳'},
      {'code': 'INR', 'name': 'Indian Rupee', 'symbol': '₹', 'flag': '🇮🇳'},
      {'code': 'AED', 'name': 'UAE Dirham', 'symbol': 'د.إ', 'flag': '🇦🇪'},
      {'code': 'SAR', 'name': 'Saudi Riyal', 'symbol': 'ر.س', 'flag': '🇸🇦'},
      {'code': 'KWD', 'name': 'Kuwaiti Dinar', 'symbol': 'د.ك', 'flag': '🇰🇼'},
      {'code': 'QAR', 'name': 'Qatari Riyal', 'symbol': 'ر.ق', 'flag': '🇶🇦'},
      {'code': 'OMR', 'name': 'Omani Rial', 'symbol': 'ر.ع.', 'flag': '🇴🇲'},
      {'code': 'BHD', 'name': 'Bahraini Dinar', 'symbol': '.د.ب', 'flag': '🇧🇭'},
      {'code': 'SGD', 'name': 'Singapore Dollar', 'symbol': 'S\$', 'flag': '🇸🇬'},
      {'code': 'HKD', 'name': 'Hong Kong Dollar', 'symbol': 'HK\$', 'flag': '🇭🇰'},
      {'code': 'KRW', 'name': 'South Korean Won', 'symbol': '₩', 'flag': '🇰🇷'},
      {'code': 'THB', 'name': 'Thai Baht', 'symbol': '฿', 'flag': '🇹🇭'},
      {'code': 'MYR', 'name': 'Malaysian Ringgit', 'symbol': 'RM', 'flag': '🇲🇾'},
    ];

    return Consumer<CurrencyProvider>(
      builder: (context, currencyProvider, child) {
        final currentCurrency = currencyProvider.selectedCurrency;

        return Container(
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(28)),
            boxShadow: [
              BoxShadow(
                color: AppColors.neutralDark.withAlpha(30),
                blurRadius: 20,
                spreadRadius: 0,
                offset: const Offset(0, -4),
              ),
            ],
          ),
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom + AppDimensions.paddingSizeDefault,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle indicator
              Center(
                child: Container(
                  margin: EdgeInsets.only(
                    top: AppDimensions.paddingSizeDefault,
                    bottom: AppDimensions.paddingSizeSmall,
                  ),
                  width: 50,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.divider,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),

              // Title Section
              Padding(
                padding: EdgeInsets.fromLTRB(
                  AppDimensions.paddingSizeOverLarge,
                  AppDimensions.paddingSizeDefault,
                  AppDimensions.paddingSizeOverLarge,
                  AppDimensions.paddingSizeSmall,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(AppDimensions.paddingSizeSmall),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withAlpha(25),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.monetization_on_rounded,
                            color: AppColors.primary,
                            size: 24,
                          ),
                        ),
                        SizedBox(width: AppDimensions.paddingSizeDefault),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocalizations.of(context)?.translate('settings.currency') ?? 'Select Currency',
                                style: AppTextStyles.headline2.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.neutralDark,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                'Choose your preferred currency for pricing',
                                style: AppTextStyles.bodyText2.copyWith(
                                  color: AppColors.textLight,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Currency List
              Flexible(
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.55,
                  ),
                  margin: EdgeInsets.symmetric(horizontal: AppDimensions.paddingSizeDefault),
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const BouncingScrollPhysics(),
                    itemCount: currencies.length,
                    separatorBuilder: (context, index) => SizedBox(height: AppDimensions.paddingSizeSmall),
                    itemBuilder: (context, index) {
                      final currency = currencies[index];
                      final isSelected = currentCurrency == currency['code'];

                      return _CurrencyCard(
                        currency: currency,
                        isSelected: isSelected,
                        onTap: () {
                          currencyProvider.setCurrency(currency['code']!);
                          Navigator.pop(context);

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Currency changed to ${currency['name']} (${currency['symbol']})',
                                style: AppTextStyles.bodyText1.copyWith(color: Colors.white),
                              ),
                              duration: const Duration(seconds: 2),
                              behavior: SnackBarBehavior.floating,
                              backgroundColor: AppColors.primary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              margin: EdgeInsets.all(AppDimensions.paddingSizeDefault),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ),

              SizedBox(height: AppDimensions.paddingSizeOverLarge),

              // Close button
              Padding(
                padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingSizeOverLarge),
                child: SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingSizeDefault),
                      backgroundColor: AppColors.neutralLight,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)?.translate('common.close') ?? 'Close',
                      style: AppTextStyles.bodyText1.copyWith(
                        color: AppColors.textLight,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),

              SizedBox(height: AppDimensions.paddingSizeDefault),
            ],
          ),
        );
      },
    );
  }
}

class _CurrencyCard extends StatefulWidget {
  final Map<String, String> currency;
  final bool isSelected;
  final VoidCallback onTap;

  const _CurrencyCard({
    required this.currency,
    required this.isSelected,
    required this.onTap,
  });

  @override
  State<_CurrencyCard> createState() => _CurrencyCardState();
}

class _CurrencyCardState extends State<_CurrencyCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _controller.forward(),
            onTapUp: (_) => _controller.reverse(),
            onTapCancel: () => _controller.reverse(),
            onTap: widget.onTap,
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingSizeDefault),
              decoration: BoxDecoration(
                color: widget.isSelected
                    ? AppColors.primary.withAlpha(25)
                    : AppColors.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: widget.isSelected
                      ? AppColors.primary
                      : AppColors.divider,
                  width: widget.isSelected ? 2 : 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.neutralDark.withAlpha(8),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Flag Container
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppColors.divider,
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.neutralDark.withAlpha(12),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        widget.currency['flag']!,
                        style: const TextStyle(fontSize: 28),
                      ),
                    ),
                  ),

                  SizedBox(width: AppDimensions.paddingSizeDefault),

                  // Currency Information
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          widget.currency['name']!,
                          style: AppTextStyles.subtitle1.copyWith(
                            fontWeight: FontWeight.w600,
                            color: widget.isSelected
                                ? AppColors.primary
                                : AppColors.neutralDark,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4),
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: AppDimensions.paddingSizeSmall,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.secondary.withAlpha(25),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                widget.currency['code']!,
                                style: AppTextStyles.caption.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.secondary,
                                ),
                              ),
                            ),
                            SizedBox(width: AppDimensions.paddingSizeSmall),
                            Text(
                              widget.currency['symbol']!,
                              style: AppTextStyles.bodyText2.copyWith(
                                fontWeight: FontWeight.w600,
                                color: AppColors.textLight,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Selection Indicator
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: EdgeInsets.all(AppDimensions.paddingSizeSmall),
                    decoration: BoxDecoration(
                      color: widget.isSelected
                          ? AppColors.primary
                          : AppColors.primary.withAlpha(25),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      widget.isSelected
                          ? Icons.check_rounded
                          : Icons.arrow_forward_ios_rounded,
                      color: widget.isSelected
                          ? Colors.white
                          : AppColors.primary,
                      size: 18,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}