import 'package:flutter/material.dart';
import 'package:hotel_booking/providers/home_provider.dart';
import 'package:hotel_booking/providers/hotel_listscreen_provider.dart';
import 'package:hotel_booking/screens/favorites%20screen/favorites_screen.dart';
import 'package:hotel_booking/screens/homescreen/widgets/date_selection_widget.dart';
import 'package:hotel_booking/screens/homescreen/widgets/guest_selection_widget.dart';
import 'package:hotel_booking/screens/hotel%20detail%20screen/hotel_detail_screen.dart';
import 'package:hotel_booking/screens/mapview%20screen/map_view_screen.dart';
import 'package:hotel_booking/screens/hotel%20list%20screen/widgets/filter_options_widget.dart';
import 'package:hotel_booking/screens/hotel%20list%20screen/widgets/hotelcard_widget.dart';
import 'package:hotel_booking/screens/profile%20screen/profile_screen.dart';
import 'package:provider/provider.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/constants/app_text_styles.dart';
import 'package:hotel_booking/constants/app_dimensions.dart';
import 'package:hotel_booking/screens/hotel%20list%20screen/widgets/loading_indicator.dart';

class HotelListScreen extends StatelessWidget {
  const HotelListScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get the HomeProvider to access the selected values
    final homeProvider = Provider.of<HomeProvider>(context, listen: false);

    return ChangeNotifierProvider(
      create: (_) => HotelListProvider()
        ..setDestination(homeProvider.destinationController.text)
        ..setCheckInDate(homeProvider.checkInDate)
        ..setCheckOutDate(homeProvider.checkOutDate)
        ..setGuests(homeProvider.rooms
            .fold(0, (sum, room) => sum + room.adults + room.children)),
      child: const _HotelListScreenContent(),
    );
  }
}

class _HotelListScreenContent extends StatefulWidget {
  const _HotelListScreenContent({Key? key}) : super(key: key);

  @override
  _HotelListScreenContentState createState() => _HotelListScreenContentState();
}

class _HotelListScreenContentState extends State<_HotelListScreenContent> {
  bool _isSearchExpanded = false;

  void _toggleSearchExpand() {
    setState(() {
      _isSearchExpanded = !_isSearchExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    final sortOptions = [
      {'icon': Icons.star, 'label': 'Rating', 'value': 'rating'},
      {
        'icon': Icons.trending_up,
        'label': 'Price: High to Low',
        'value': 'price_desc'
      },
      {
        'icon': Icons.trending_down,
        'label': 'Price: Low to High',
        'value': 'price_asc'
      },
      {'icon': Icons.thumb_up, 'label': 'Popularity', 'value': 'popularity'},
      {'icon': Icons.discount, 'label': 'Deals', 'value': 'deals'},
      {'icon': Icons.location_on, 'label': 'Distance', 'value': 'distance'},
    ];

    return Consumer<HotelListProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: AppColors.background,
          appBar: AppBar(
            elevation: 0,
            backgroundColor: AppColors.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(20),
              ),
            ),
            title: InkWell(
              onTap: _toggleSearchExpand,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(40),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: Colors.white.withAlpha(60), width: 1),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(30),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.location_on,
                        size: 12,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 6),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Hotels in ${provider.destination}',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.date_range,
                                size: 9,
                                color: Colors.white.withAlpha(200),
                              ),
                              SizedBox(width: 2),
                              Flexible(
                                child: Text(
                                  '${provider.checkInDate != null ? provider.formatDate(provider.checkInDate!) : ''} - ${provider.checkOutDate != null ? provider.formatDate(provider.checkOutDate!) : ''}',
                                  style: TextStyle(
                                    fontSize: 9,
                                    color: Colors.white.withAlpha(220),
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              SizedBox(width: 4),
                              Icon(
                                Icons.person_outline,
                                size: 9,
                                color: Colors.white.withAlpha(200),
                              ),
                              SizedBox(width: 2),
                              Text(
                                '${provider.guests} guest${provider.guests > 1 ? 's' : ''}',
                                style: TextStyle(
                                  fontSize: 9,
                                  color: Colors.white.withAlpha(220),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 4),
                    AnimatedRotation(
                      turns: _isSearchExpanded ? 0.5 : 0,
                      duration: const Duration(milliseconds: 300),
                      child: Icon(
                        Icons.keyboard_arrow_down,
                        size: 14,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            centerTitle: true,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context),
            ),
            actions: [
              IconButton(
                  onPressed: () =>
                      _navigateToScreen(context, const FavoritesScreen()),
                  icon: const Icon(Icons.favorite_outline)),
              IconButton(
                  onPressed: () =>
                      _navigateToScreen(context, const ProfileScreen()),
                  icon: const Icon(Icons.person_outline))
            ],
          ),
          body: SizedBox(
            child: Column(
              children: [
                if (_isSearchExpanded)
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    margin: EdgeInsets.fromLTRB(
                        0, 0, 0, AppDimensions.paddingSizeSmall),
                    padding: EdgeInsets.all(AppDimensions.paddingSizeDefault),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(20),
                        bottomRight: Radius.circular(20),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.neutralDark.withAlpha(13),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDestinationTextField(provider),
                        SizedBox(height: AppDimensions.paddingSizeSmall),
                        _buildDateSelectors(context, provider),
                        SizedBox(height: AppDimensions.paddingSizeSmall),
                        _buildGuestSelector(context, provider),
                        SizedBox(height: AppDimensions.paddingSizeDefault),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _toggleSearchExpand,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.secondary,
                              foregroundColor: Colors.white,
                              padding: EdgeInsets.symmetric(
                                  vertical: AppDimensions.paddingSizeDefault),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                    AppDimensions.radiusDefault),
                              ),
                              elevation: 2,
                            ),
                            child: Text(
                              'Apply Changes',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: AppDimensions.fontSizeLarge,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                SizedBox(height: 10),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 15),
                  width: double.infinity,
                  height: 60,
                  child: Row(
                    children: [
                      InkWell(onTap: () {
                            Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => FilterOptionsWidget()),
    );
                      },
                        child: CircleAvatar(
                          child: Icon(Icons.tune_rounded),
                        ),
                      ),
                      Expanded(
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: sortOptions.length,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          physics: const BouncingScrollPhysics(),
                          itemBuilder: (context, index) {
                            final option = sortOptions[index];
                            final isSelected =
                                provider.sortOption == option['value'];

                            return AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              margin: const EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 10),
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: () {
                                    provider.setSortOption(
                                        option['value'] as String);
                                  },
                                  borderRadius: BorderRadius.circular(20),
                                  child: Ink(
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? AppColors.secondary.withAlpha(26)
                                          : Colors.white,
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: isSelected
                                            ? AppColors.secondary
                                            : AppColors.divider,
                                        width: isSelected ? 1.5 : 1,
                                      ),
                                      boxShadow: isSelected
                                          ? [
                                              BoxShadow(
                                                color: AppColors.secondary.withAlpha(51),
                                                blurRadius: 4,
                                                offset: Offset(0, 2),
                                              ),
                                            ]
                                          : null,
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    child: Row(
                                      children: [
                                        Icon(
                                          option['icon'] as IconData,
                                          color: isSelected
                                              ? AppColors.secondary
                                              : AppColors.textLight,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 6),
                                        Text(
                                          option['label'] as String,
                                          style: TextStyle(
                                            color: isSelected
                                                ? AppColors.secondary
                                                : AppColors.text,
                                            fontWeight: isSelected
                                                ? FontWeight.w600
                                                : FontWeight.w500,
                                            fontSize: 12,
                                            letterSpacing: isSelected ? 0.3 : 0,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: _buildHotelList(context, provider),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _navigateToScreen(BuildContext context, Widget screen) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }

  Widget _buildDestinationTextField(HotelListProvider provider) {
    // Get the HomeProvider to access the destination value and places
    final homeProvider = Provider.of<HomeProvider>(context, listen: false);

    return Autocomplete<String>(
      initialValue: TextEditingValue(
        text: provider.destination,
        selection: TextSelection.fromPosition(
          TextPosition(offset: provider.destination.length),
        ),
      ),
      optionsBuilder: (TextEditingValue textEditingValue) {
        // Get all places from the HomeProvider
        final List<String> allPlaces = homeProvider.places
            .expand((place) => place.places ?? [])
            .cast<String>()
            .toList();

        if (textEditingValue.text.isEmpty) {
          return allPlaces;
        }

        return allPlaces.where((option) =>
            option.toLowerCase().contains(textEditingValue.text.toLowerCase()));
      },
      onSelected: (String selection) {
        // Update both providers
        provider.setDestination(selection);
        homeProvider.setDestination(selection);
      },
      fieldViewBuilder: (
        BuildContext context,
        TextEditingController fieldController,
        FocusNode fieldFocusNode,
        VoidCallback onFieldSubmitted,
      ) {
        return TextField(
          controller: fieldController,
          focusNode: fieldFocusNode,
          onChanged: (value) {
            // Update both providers
            provider.setDestination(value);
            homeProvider.setDestination(value);
          },
          decoration: InputDecoration(
            labelText: 'Destination',
            labelStyle: TextStyle(color: AppColors.textLight),
            hintText: 'Where are you going?',
            prefixIcon:
                Icon(Icons.location_on_outlined, color: AppColors.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: AppColors.surface,
            contentPadding: EdgeInsets.symmetric(
                vertical: AppDimensions.paddingSizeDefault,
                horizontal: AppDimensions.paddingSizeDefault),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
              borderSide: BorderSide(color: AppColors.primary, width: 1.5),
            ),
          ),
        );
      },
      optionsViewBuilder: (
        BuildContext context,
        AutocompleteOnSelected<String> onSelected,
        Iterable<String> options,
      ) {
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              color: AppColors.background,
              width: MediaQuery.of(context).size.width - 64,
              constraints: BoxConstraints(
                maxHeight: 200,
              ),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: options.length,
                itemBuilder: (BuildContext context, int index) {
                  final String option = options.elementAt(index);
                  return InkWell(
                    onTap: () {
                      onSelected(option);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Text(
                        option,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.text,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDateSelectors(BuildContext context, HotelListProvider provider) {
    return InkWell(
      onTap: () {
        showModalBottomSheet(
          backgroundColor: AppColors.primary,
          context: context,
          isScrollControlled: true,
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height * 0.3,
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          builder: (context) {
            // Use the DateSelectionWidget from the home screen
            return Consumer<HomeProvider>(
              builder: (context, homeProvider, _) {
                // Sync the HomeProvider with HotelListProvider
                if (provider.checkInDate != null &&
                    homeProvider.checkInDate != provider.checkInDate) {
                  homeProvider.setCheckInDate(provider.checkInDate!);
                }
                if (provider.checkOutDate != null &&
                    homeProvider.checkOutDate != provider.checkOutDate) {
                  homeProvider.setCheckOutDate(provider.checkOutDate!);
                }

                return DateSelectionWidget();
              },
            );
          },
        ).then((_) {
          // After the date selection is closed, update the HotelListProvider
          if (context.mounted) {
            final homeProvider =
                Provider.of<HomeProvider>(context, listen: false);
            provider.setCheckInDate(homeProvider.checkInDate);
            provider.setCheckOutDate(homeProvider.checkOutDate);
          }
        });
      },
      child: Row(
        children: [
          Expanded(
            child: _buildDateSelector(
              context: context,
              title: 'Check-in',
              date: provider.checkInDate,
            ),
          ),
          SizedBox(width: AppDimensions.paddingSizeSmall),
          Expanded(
            child: _buildDateSelector(
              context: context,
              title: 'Check-out',
              date: provider.checkOutDate,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelector({
    required BuildContext context,
    required String title,
    required DateTime? date,
  }) {
    final provider = Provider.of<HotelListProvider>(context, listen: false);

    return Container(
      padding: EdgeInsets.symmetric(
          vertical: AppDimensions.paddingSizeSmall,
          horizontal: AppDimensions.paddingSizeSmall),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
        border: Border.all(
          color: AppColors.primary.withAlpha(76),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.calendar_today_outlined,
            size: 18,
            color: AppColors.primary,
          ),
          SizedBox(width: AppDimensions.paddingSizeSmall),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: AppColors.primary,
                  fontSize: AppDimensions.fontSizeSmall,
                ),
              ),
              SizedBox(height: 4),
              Text(
                date != null ? provider.formatDate(date) : 'Select date',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.text,
                  fontSize: AppDimensions.fontSizeSmall,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGuestSelector(BuildContext context, HotelListProvider provider) {
    return InkWell(
      onTap: () {
        showModalBottomSheet(
          backgroundColor: AppColors.primary,
          context: context,
          isScrollControlled: true,
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height * 0.3,
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          builder: (context) {
            // Use the GuestSelectionWidget from the home screen
            return const GuestSelectionWidget();
          },
        ).then((_) {
          // After the guest selection is closed, update the HotelListProvider
          if (context.mounted) {
            final homeProvider =
                Provider.of<HomeProvider>(context, listen: false);
            // Calculate total guests from rooms
            int totalGuests = homeProvider.rooms
                .fold(0, (sum, room) => sum + room.adults + room.children);
            provider.setGuests(totalGuests);
          }
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
            vertical: AppDimensions.paddingSizeSmall,
            horizontal: AppDimensions.paddingSizeSmall),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
          border: Border.all(
            color: AppColors.primary.withAlpha(76),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.person_outline,
              size: 20,
              color: AppColors.primary,
            ),
            SizedBox(width: AppDimensions.paddingSizeDefault),
            Text(
              '${provider.guests} Guest${provider.guests > 1 ? 's' : ''}',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.text,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.primary,
              size: 24,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHotelList(BuildContext context, HotelListProvider provider) {
    if (provider.isLoading) {
      return const LoadingIndicator();
    }

    if (provider.error != null) {
      return _buildErrorState(context, provider);
    }

    if (!provider.hasHotels) {
      return _buildEmptyState(context);
    }

    return Stack(
      children: [
        // Hotel list
        ListView.builder(
          padding: EdgeInsets.only(
            top: AppDimensions.paddingSizeDefault,
            bottom: 80 +
                MediaQuery.of(context)
                    .padding
                    .bottom, // Add padding for the filter bar at bottom + safe area
          ),
          itemCount: provider.hotels.length,
          itemBuilder: (context, index) {
            final hotel = provider.hotels[index];
            return HotelCard(
              hotel: hotel,
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => HotelDetailScreen(hotel: hotel),
                    ));
              },
            );
          },
        ),

        // Filter and sort bar at bottom
        Positioned(
          bottom: 0,
          // left: 0,
          right: 148,
          child: SafeArea(
            child: Container(
              width: 100,
              height: 60,
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.secondary.withAlpha(230), // Lighter shade of secondary
                    AppColors.primary,
                  ],
                ),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.secondary.withAlpha(76), // AppColors.secondary with opacity
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    // Navigate to map view
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => MapViewScreen(
                          hotels: provider.hotels,
                          destination: provider.destination,
                        ),
                      ),
                    );
                  },
                  borderRadius: BorderRadius.circular(15),
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: const [
                        Icon(
                          Icons.map_outlined,
                          color: Colors.white,
                          size: 18,
                        ),
                        SizedBox(width: 6),
                        Text(
                          'Map',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(BuildContext context, HotelListProvider provider) {
    return Center(
      child: _buildMessageContainer(
        icon: const Icon(
          Icons.error_outline,
          size: 64,
          color: AppColors.error,
        ),
        title: 'Error loading hotels',
        message: provider.error!,
        buttonText: 'Retry',
        onButtonPressed: provider.retryLoadingHotels,
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: _buildMessageContainer(
        icon: const Icon(
          Icons.search_off,
          size: 64,
          color: AppColors.textLight,
        ),
        title: 'No hotels found',
        message: 'Try changing your search criteria',
        buttonText: 'Go Back',
        onButtonPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildMessageContainer({
    required Widget icon,
    required String title,
    required String message,
    required String buttonText,
    required VoidCallback onButtonPressed,
  }) {
    return Container(
      margin: EdgeInsets.all(AppDimensions.paddingSizeLarge),
      padding: EdgeInsets.all(AppDimensions.paddingSizeLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusExtraLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.neutralDark.withAlpha(13),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon,
          SizedBox(height: AppDimensions.paddingSizeDefault),
          Text(
            title,
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppDimensions.paddingSizeSmall),
          Text(
            message,
            style: AppTextStyles.bodyText2,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppDimensions.paddingSizeLarge),
          ElevatedButton(
            onPressed: onButtonPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.secondary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingSizeLarge,
                  vertical: AppDimensions.paddingSizeSmall),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
            child: Text(buttonText),
          ),
        ],
      ),
    );
  }
}
