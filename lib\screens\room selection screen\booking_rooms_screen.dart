import 'package:flutter/material.dart';
import 'package:hotel_booking/constants/app_colors.dart';
import 'package:hotel_booking/models/hotel_details.dart';
import 'package:hotel_booking/providers/room_selection_provider.dart';
import 'package:hotel_booking/screens/booking%20screen/booking_screen.dart';
import 'package:hotel_booking/screens/room%20selection%20screen/widget/roomcard_widget.dart';
import 'package:provider/provider.dart';

class BookingRoomsScreen extends StatefulWidget {
  final InventoryInfoList? hotel;

  const BookingRoomsScreen({
    super.key,
    this.hotel,
  });

  @override
  State<BookingRoomsScreen> createState() => _BookingRoomsScreenState();
}

class _BookingRoomsScreenState extends State<BookingRoomsScreen> {
  // Track selected room and meal option
  String selectedRoomType = 'Deluxe Room';

  @override
  void initState() {
    super.initState();
    // Load hotel rooms data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<RoomSelectionProvider>(context, listen: false)
          .loadHotelRoomsFromJson();
    });
  }

  // Sort options
  String _selectedSortOption = 'free_breakfast';
  final List<Map<String, dynamic>> _sortOptions = [
    {
      'id': 'free_breakfast',
      'label': 'Free Breakfast',
      'icon': Icons.free_breakfast
    },
    {
      'id': 'free_parking_wifi_breakfast',
      'label': 'Free Parking, WiFi & Breakfast',
      'icon': Icons.local_parking
    },
    {'id': 'breakfast', 'label': 'Breakfast', 'icon': Icons.breakfast_dining},
    {
      'id': 'bed_and_breakfast',
      'label': 'Bed & Breakfast',
      'icon': Icons.hotel
    },
    {
      'id': 'free_parking_wifi_room',
      'label': 'Free Parking, WiFi & Room Only',
      'icon': Icons.wifi
    },
    {'id': 'room_only', 'label': 'Room Only', 'icon': Icons.bedroom_parent},
  ];

  // Build sort options bar
  Widget _buildSortOptionsBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Row(
        children: [
          Container(
              height: 50,
              width: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: AppColors.primary,
              ),
              child: Icon(Icons.sort, color: Colors.white)),
          SizedBox(
            width: 10,
          ),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _sortOptions.map((option) {
                  final bool isSelected = _selectedSortOption == option['id'];
                  return InkWell(
                    onTap: () {
                      setState(() {
                        _selectedSortOption = option['id'] as String;
                      });
                    },
                    child: Container(
                      height: 50,
                      margin: const EdgeInsets.only(right: 10),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.primary : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected
                              ? AppColors.secondary
                              : Colors.grey.shade300,
                        ),
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: AppColors.secondary
                                      .withAlpha(51), // 0.2 * 255 = 51
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ]
                            : null,
                      ),
                      child: Row(
                        children: [
                          Icon(
                            option['icon'] as IconData,
                            size: 16,
                            color: isSelected
                                ? Colors.white
                                : Colors.grey.shade700,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            option['label'] as String,
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isSelected
                                  ? Colors.white
                                  : Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RoomSelectionProvider>(
      builder: (context, RoomSelectionProvider, child) {
        return Scaffold(
          backgroundColor: AppColors.divider,
          appBar: AppBar(
            title: const Text('Available Rooms'),
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            elevation: 0,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(20),
              ),
            ),
          ),
          body: Column(
            children: [
              // Sort options bar
              _buildSortOptionsBar(),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      setState(() {
                        _selectedSortOption = 'all';
                      });
                    },
                    child: Row(
                      children: [
                        Icon(Icons.filter_list_off,
                            size: 18, color: AppColors.secondary),
                        const SizedBox(width: 4),
                        Text(
                          'Clear Filters',
                          style: TextStyle(
                            decoration: TextDecoration.underline,
                            fontSize: 16,
                            color: AppColors.secondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 20),
                ],
              ),

              // Content
              Expanded(
                child: ListView.separated(
                  itemCount: RoomSelectionProvider.rooms.length,
                  itemBuilder: (context, index) => RoomcardWidget(room: RoomSelectionProvider.rooms[index],
                    roomSelectionProvider: RoomSelectionProvider,
                  ),
                  separatorBuilder: (context, index) => SizedBox(
                    height: 20,
                  ),
                ),
              ),
            
          
            ],
          ),
        );
      },
    );
  }
}
